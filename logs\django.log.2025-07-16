[INFO] 2025-07-16 19:51:04,151 django.utils.autoreload | Watching for file changes with StatReloader
[WARNING] 2025-07-16 19:51:59,228 django.request | Not Found: /
[WARNING] 2025-07-16 19:51:59,230 django.server | "GET / HTTP/1.1" 404 15732
[INFO] 2025-07-16 19:51:59,282 django.server | "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-16 19:51:59,289 django.server | "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-16 19:51:59,708 django.server | "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-16 19:51:59,777 django.server | "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[WARNING] 2025-07-16 19:52:00,269 django.request | Not Found: /favicon.ico
[WARNING] 2025-07-16 19:52:00,270 django.server | "GET /favicon.ico HTTP/1.1" 404 15794
[INFO] 2025-07-16 19:52:35,506 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 38082
[INFO] 2025-07-16 19:58:38,443 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\models.py changed, reloading.
[INFO] 2025-07-16 19:58:40,969 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 19:59:08,057 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\models.py changed, reloading.
[INFO] 2025-07-16 19:59:10,652 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 19:59:22,633 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\forms.py changed, reloading.
[INFO] 2025-07-16 19:59:25,509 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 19:59:34,061 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\views.py changed, reloading.
[INFO] 2025-07-16 19:59:36,537 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 19:59:56,648 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\views.py changed, reloading.
[INFO] 2025-07-16 19:59:59,168 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:00:13,501 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\urls.py changed, reloading.
[INFO] 2025-07-16 20:00:16,210 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:15:14,315 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:16:32,685 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:17:21,531 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 18215
[INFO] 2025-07-16 20:17:45,471 django.server | - Broken pipe from ('127.0.0.1', 5652)
[INFO] 2025-07-16 20:17:48,429 django.server | "GET /knowledge_core/category/new/ HTTP/1.1" 302 0
[WARNING] 2025-07-16 20:17:48,658 django.request | Not Found: /accounts/login/
[WARNING] 2025-07-16 20:17:48,659 django.server | "GET /accounts/login/?next=/knowledge_core/category/new/ HTTP/1.1" 404 15880
[INFO] 2025-07-16 20:18:31,104 django.server | - Broken pipe from ('127.0.0.1', 5682)
[INFO] 2025-07-16 20:20:27,744 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 18215
[INFO] 2025-07-16 20:20:27,942 django.server | "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 200 29444
[INFO] 2025-07-16 20:20:28,005 django.server | "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 200 14803
[INFO] 2025-07-16 20:20:57,579 django.server | "GET /static/debug_toolbar/css/print.css HTTP/1.1" 200 43
[INFO] 2025-07-16 20:20:57,680 django.server | "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 200 4692
[WARNING] 2025-07-16 20:21:01,840 django.request | Not Found: /favicon.ico
[WARNING] 2025-07-16 20:21:01,841 django.server | "GET /favicon.ico HTTP/1.1" 404 15794
[INFO] 2025-07-16 20:32:17,777 django.server | "GET /knowledge_core/category/new/ HTTP/1.1" 302 0
[WARNING] 2025-07-16 20:32:18,026 django.request | Not Found: /accounts/login/
[WARNING] 2025-07-16 20:32:18,026 django.server | "GET /accounts/login/?next=/knowledge_core/category/new/ HTTP/1.1" 404 15880
[INFO] 2025-07-16 20:32:43,534 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:33:06,665 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 18215
[INFO] 2025-07-16 20:33:08,779 django.server | "GET /knowledge_core/category/new/ HTTP/1.1" 302 0
[WARNING] 2025-07-16 20:33:08,816 django.request | Not Found: /accounts/login/
[WARNING] 2025-07-16 20:33:08,816 django.server | "GET /accounts/login/?next=/knowledge_core/category/new/ HTTP/1.1" 404 15879
[INFO] 2025-07-16 20:35:27,240 django.utils.autoreload | D:\projects\gongvip-note\gongvip_note\urls.py changed, reloading.
[INFO] 2025-07-16 20:35:28,630 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:35:43,950 django.utils.autoreload | D:\projects\gongvip-note\gongvip_note\settings.py changed, reloading.
[INFO] 2025-07-16 20:35:45,509 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:36:53,190 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\urls.py changed, reloading.
[INFO] 2025-07-16 20:36:54,774 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:37:10,982 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\urls.py changed, reloading.
[INFO] 2025-07-16 20:37:12,353 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:38:34,260 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 20:39:07,305 django.server | "GET /accounts/login/ HTTP/1.1" 200 21863
[INFO] 2025-07-16 20:39:36,958 django.server | - Broken pipe from ('127.0.0.1', 6980)
[INFO] 2025-07-16 20:39:39,896 django.server | "GET /knowledge_core/category/new/ HTTP/1.1" 302 0
[INFO] 2025-07-16 20:39:39,925 django.server | "GET /accounts/login/?next=/knowledge_core/category/new/ HTTP/1.1" 200 22022
[INFO] 2025-07-16 20:39:50,653 django.server | "GET /knowledge_core/category/new/ HTTP/1.1" 302 0
[INFO] 2025-07-16 20:39:50,675 django.server | "GET /accounts/login/?next=/knowledge_core/category/new/ HTTP/1.1" 200 22022
[INFO] 2025-07-16 20:41:15,512 django.server | - Broken pipe from ('127.0.0.1', 7028)
[INFO] 2025-07-16 20:54:43,124 django.server | "POST /accounts/login/?next=/knowledge_core/category/new/ HTTP/1.1" 302 0
[INFO] 2025-07-16 20:54:43,366 django.server | "GET /knowledge_core/category/new/ HTTP/1.1" 200 22049
[INFO] 2025-07-16 20:55:12,214 django.server | "POST /knowledge_core/category/new/ HTTP/1.1" 302 0
[INFO] 2025-07-16 20:55:12,282 django.server | "GET /knowledge_core/category/ce-shi-fen-lei/knowledges/ HTTP/1.1" 200 20509
[INFO] 2025-07-16 20:55:21,943 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 200 25236
[INFO] 2025-07-16 20:55:33,642 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 200 25235
[INFO] 2025-07-16 20:55:53,918 django.server | "POST /knowledge_core/knowledge/new/ HTTP/1.1" 200 25656
[INFO] 2025-07-16 20:56:10,514 django.server | "POST /knowledge_core/knowledge/new/ HTTP/1.1" 200 25656
[INFO] 2025-07-16 20:56:16,902 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 20917
[INFO] 2025-07-16 20:56:24,978 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 20917
[INFO] 2025-07-16 20:56:27,113 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 39511
[INFO] 2025-07-16 20:56:34,139 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 20915
[INFO] 2025-07-16 20:56:35,889 django.server | "GET /knowledge_core/category/ce-shi-fen-lei/ HTTP/1.1" 200 21125
[INFO] 2025-07-16 20:56:37,672 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 200 25235
[INFO] 2025-07-16 20:56:49,369 django.server | "POST /knowledge_core/knowledge/new/ HTTP/1.1" 200 25655
[WARNING] 2025-07-16 20:57:35,818 django.request | Not Found: /.well-known/appspecific/com.chrome.devtools.json
[WARNING] 2025-07-16 20:57:35,837 django.server | "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 16093
[INFO] 2025-07-16 20:57:45,519 django.server | "POST /knowledge_core/knowledge/new/ HTTP/1.1" 200 25655
[WARNING] 2025-07-16 20:57:45,602 django.request | Not Found: /.well-known/appspecific/com.chrome.devtools.json
[WARNING] 2025-07-16 20:57:45,602 django.server | "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 16093
[INFO] 2025-07-16 21:08:54,577 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 39511
[INFO] 2025-07-16 21:08:56,513 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 39511
[INFO] 2025-07-16 21:08:59,364 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 20915
[INFO] 2025-07-16 21:09:00,998 django.server | "GET /knowledge_core/category/ce-shi-fen-lei/ HTTP/1.1" 200 21125
[INFO] 2025-07-16 21:09:02,811 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 200 24117
[INFO] 2025-07-16 21:09:14,323 django.server | "POST /knowledge_core/knowledge/new/ HTTP/1.1" 302 0
[INFO] 2025-07-16 21:09:14,461 django.server | "GET /knowledge_core/knowledge/ce-shi/ HTTP/1.1" 200 19796
[INFO] 2025-07-16 21:09:22,209 django.server | "GET /knowledge_core/category/ce-shi-fen-lei/knowledges/ HTTP/1.1" 200 21467
[INFO] 2025-07-16 21:09:25,119 django.server | "GET /knowledge_core/knowledge/ce-shi/ HTTP/1.1" 200 19436
[INFO] 2025-07-16 21:09:27,322 django.server | "GET /knowledge_core/category/ce-shi-fen-lei/knowledges/ HTTP/1.1" 200 21467
[INFO] 2025-07-16 21:09:31,417 django.server | "GET /knowledge_core/knowledge/ce-shi/ HTTP/1.1" 200 19436
[INFO] 2025-07-16 21:09:33,758 django.server | "GET /knowledge_core/category/ce-shi-fen-lei/knowledges/ HTTP/1.1" 200 21467
[INFO] 2025-07-16 21:09:34,833 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 39463
[INFO] 2025-07-16 21:09:37,467 django.server | "GET /knowledge_core/knowledge/ce-shi/ HTTP/1.1" 200 19436
[INFO] 2025-07-16 21:09:42,992 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 39463
[INFO] 2025-07-16 21:09:46,561 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 39463
[INFO] 2025-07-16 21:09:51,215 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 200 24117
[INFO] 2025-07-16 21:15:53,173 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 21:16:35,020 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 302 0
[INFO] 2025-07-16 21:16:35,051 django.server | "GET /accounts/login/?next=/knowledge_core/knowledge/new/ HTTP/1.1" 200 22024
[INFO] 2025-07-16 21:16:45,449 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 200 33260
[INFO] 2025-07-16 21:17:50,409 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\views.py changed, reloading.
[INFO] 2025-07-16 21:17:51,890 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 21:18:07,458 django.utils.autoreload | D:\projects\gongvip-note\knowledge_core\urls.py changed, reloading.
[INFO] 2025-07-16 21:18:08,848 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-16 21:18:22,326 django.server | "GET /knowledge_core/markdown-demo/ HTTP/1.1" 200 29070
[INFO] 2025-07-16 21:18:42,660 django.server | "GET /knowledge_core/markdown-demo/ HTTP/1.1" 200 29203
[INFO] 2025-07-16 22:29:05,161 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 39743
[INFO] 2025-07-16 22:29:07,737 django.server | "GET /knowledge_core/knowledge/ce-shi/ HTTP/1.1" 200 19715
[INFO] 2025-07-16 22:29:14,929 django.server | "GET /knowledge_core/categories/ HTTP/1.1" 200 21194
[INFO] 2025-07-16 22:29:17,236 django.server | "GET /knowledge_core/category/ce-shi-fen-lei/ HTTP/1.1" 200 21626
[INFO] 2025-07-16 22:29:19,807 django.server | "GET /knowledge_core/knowledge/ce-shi/ HTTP/1.1" 200 19715
[INFO] 2025-07-16 22:29:23,688 django.server | "GET /knowledge_core/knowledge/new/ HTTP/1.1" 200 33538
