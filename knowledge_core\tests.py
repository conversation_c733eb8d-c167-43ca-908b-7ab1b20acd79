from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from .models import Category, KnowledgeEntry, Tag


class CategoryModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )

    def test_category_creation(self):
        """测试分类创建"""
        category = Category.objects.create(
            name='测试分类',
            description='这是一个测试分类'
        )
        self.assertEqual(category.name, '测试分类')
        self.assertEqual(category.description, '这是一个测试分类')
        self.assertTrue(category.slug)  # slug应该自动生成

    def test_category_slug_generation(self):
        """测试slug自动生成"""
        category = Category.objects.create(name='Python Programming')
        self.assertEqual(category.slug, 'python-programming')

        # 测试中文slug生成
        category_cn = Category.objects.create(name='Python编程')
        self.assertTrue(category_cn.slug)  # 只验证slug存在，不验证具体内容

    def test_category_hierarchy(self):
        """测试分类层次结构"""
        parent = Category.objects.create(name='编程语言')
        child = Category.objects.create(name='Python', parent=parent)

        self.assertEqual(child.parent, parent)
        self.assertEqual(parent.category_set.first(), child)

    def test_category_str_method(self):
        """测试__str__方法"""
        category = Category.objects.create(name='测试分类')
        self.assertEqual(str(category), '测试分类')


class CategoryViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            description='测试描述'
        )

    def test_category_list_view(self):
        """测试分类列表页面"""
        response = self.client.get(reverse('knowledge_core:category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试分类')

    def test_category_detail_view(self):
        """测试分类详情页面"""
        response = self.client.get(
            reverse('knowledge_core:category_detail', args=[self.category.slug])
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试分类')
        self.assertContains(response, '测试描述')

    def test_category_create_requires_login(self):
        """测试创建分类需要登录"""
        response = self.client.get(reverse('knowledge_core:category_create'))
        self.assertEqual(response.status_code, 302)  # 重定向到登录页面

    def test_category_create_authenticated(self):
        """测试登录用户可以访问创建分类页面"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('knowledge_core:category_create'))
        self.assertEqual(response.status_code, 200)

    def test_category_create_post(self):
        """测试创建分类POST请求"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(reverse('knowledge_core:category_create'), {
            'name': '新分类',
            'description': '新分类描述'
        })
        self.assertEqual(response.status_code, 302)  # 重定向到详情页面

        # 验证分类已创建
        category = Category.objects.get(name='新分类')
        self.assertEqual(category.description, '新分类描述')


class KnowledgeEntryModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.category = Category.objects.create(name='测试分类')

    def test_knowledge_entry_creation(self):
        """测试知识条目创建"""
        knowledge = KnowledgeEntry.objects.create(
            title='测试知识条目',
            content='这是测试内容',
            created_by=self.user,
            category=self.category
        )
        self.assertEqual(knowledge.title, '测试知识条目')
        self.assertEqual(knowledge.content, '这是测试内容')
        self.assertEqual(knowledge.created_by, self.user)
        self.assertEqual(knowledge.category, self.category)
        self.assertTrue(knowledge.slug)

    def test_knowledge_entry_slug_generation(self):
        """测试slug自动生成"""
        knowledge = KnowledgeEntry.objects.create(
            title='Test Knowledge Entry',
            content='Test content',
            created_by=self.user
        )
        self.assertEqual(knowledge.slug, 'test-knowledge-entry')

    def test_knowledge_entry_str_method(self):
        """测试__str__方法"""
        knowledge = KnowledgeEntry.objects.create(
            title='测试知识条目',
            content='测试内容',
            created_by=self.user
        )
        self.assertEqual(str(knowledge), '测试知识条目')


class KnowledgeEntryViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.other_user = User.objects.create_user(
            username='otheruser',
            password='testpass123'
        )
        self.category = Category.objects.create(name='测试分类')
        self.knowledge = KnowledgeEntry.objects.create(
            title='测试知识条目',
            content='测试内容',
            created_by=self.user,
            category=self.category
        )

    def test_knowledge_list_view(self):
        """测试知识条目列表页面"""
        response = self.client.get(reverse('knowledge_core:knowledge_entry_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试知识条目')

    def test_knowledge_detail_view(self):
        """测试知识条目详情页面"""
        response = self.client.get(
            reverse('knowledge_core:knowledge_detail', args=[self.knowledge.slug])
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试知识条目')
        self.assertContains(response, '测试内容')

    def test_knowledge_create_requires_login(self):
        """测试创建知识条目需要登录"""
        response = self.client.get(reverse('knowledge_core:knowledge_create'))
        self.assertEqual(response.status_code, 302)  # 重定向到登录页面

    def test_knowledge_create_authenticated(self):
        """测试登录用户可以访问创建页面"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('knowledge_core:knowledge_create'))
        self.assertEqual(response.status_code, 200)

    def test_knowledge_create_post(self):
        """测试创建知识条目POST请求"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(reverse('knowledge_core:knowledge_create'), {
            'title': '新知识条目',
            'content': '新知识条目内容'
        })
        self.assertEqual(response.status_code, 302)  # 重定向到详情页面

        # 验证知识条目已创建
        knowledge = KnowledgeEntry.objects.get(title='新知识条目')
        self.assertEqual(knowledge.content, '新知识条目内容')
        self.assertEqual(knowledge.created_by, self.user)

    def test_knowledge_edit_permission(self):
        """测试编辑权限检查"""
        # 其他用户尝试编辑
        self.client.login(username='otheruser', password='testpass123')
        response = self.client.get(
            reverse('knowledge_core:knowledge_edit', args=[self.knowledge.slug])
        )
        # 应该被重定向（权限不足）
        self.assertEqual(response.status_code, 302)

        # 创建者可以编辑
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('knowledge_core:knowledge_edit', args=[self.knowledge.slug])
        )
        self.assertEqual(response.status_code, 200)

    def test_knowledge_delete_permission(self):
        """测试删除权限检查"""
        # 其他用户尝试删除
        self.client.login(username='otheruser', password='testpass123')
        response = self.client.get(
            reverse('knowledge_core:knowledge_delete', args=[self.knowledge.slug])
        )
        # 应该被重定向（权限不足）
        self.assertEqual(response.status_code, 302)

        # 创建者可以删除
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(
            reverse('knowledge_core:knowledge_delete', args=[self.knowledge.slug])
        )
        self.assertEqual(response.status_code, 200)


class TagModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )

    def test_tag_creation(self):
        """测试标签创建"""
        tag = Tag.objects.create(name='Python')
        self.assertEqual(tag.name, 'Python')
        self.assertTrue(tag.slug)  # slug应该自动生成

    def test_tag_slug_generation(self):
        """测试slug自动生成"""
        tag = Tag.objects.create(name='Python编程')
        self.assertTrue(tag.slug)  # 验证slug存在

    def test_tag_str_method(self):
        """测试__str__方法"""
        tag = Tag.objects.create(name='测试标签')
        self.assertEqual(str(tag), '测试标签')


class TagViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.tag = Tag.objects.create(name='测试标签')

    def test_tag_list_view(self):
        """测试标签列表页面"""
        response = self.client.get(reverse('knowledge_core:tag_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试标签')

    def test_tag_detail_view(self):
        """测试标签详情页面"""
        response = self.client.get(
            reverse('knowledge_core:tag_detail', args=[self.tag.slug])
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试标签')

    def test_tag_create_requires_login(self):
        """测试创建标签需要登录"""
        response = self.client.get(reverse('knowledge_core:tag_create'))
        self.assertEqual(response.status_code, 302)  # 重定向到登录页面

    def test_tag_create_authenticated(self):
        """测试登录用户可以访问创建标签页面"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('knowledge_core:tag_create'))
        self.assertEqual(response.status_code, 200)

    def test_tag_create_post(self):
        """测试创建标签POST请求"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(reverse('knowledge_core:tag_create'), {
            'name': '新标签'
        })
        self.assertEqual(response.status_code, 302)  # 重定向到详情页面

        # 验证标签已创建
        tag = Tag.objects.get(name='新标签')
        self.assertEqual(tag.name, '新标签')


class AuthenticationTest(TestCase):
    def setUp(self):
        self.client = Client()

    def test_login_page_accessible(self):
        """测试登录页面可访问"""
        response = self.client.get('/accounts/login/')
        self.assertEqual(response.status_code, 200)

    def test_signup_page_accessible(self):
        """测试注册页面可访问"""
        response = self.client.get(reverse('knowledge_core:signup'))
        self.assertEqual(response.status_code, 200)
