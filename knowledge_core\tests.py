from django.test import TestCase, Client
from django.contrib.auth.models import User
from django.urls import reverse
from .models import Category, KnowledgeEntry, Tag


class CategoryModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )

    def test_category_creation(self):
        """测试分类创建"""
        category = Category.objects.create(
            name='测试分类',
            description='这是一个测试分类'
        )
        self.assertEqual(category.name, '测试分类')
        self.assertEqual(category.description, '这是一个测试分类')
        self.assertTrue(category.slug)  # slug应该自动生成

    def test_category_slug_generation(self):
        """测试slug自动生成"""
        category = Category.objects.create(name='Python Programming')
        self.assertEqual(category.slug, 'python-programming')

        # 测试中文slug生成
        category_cn = Category.objects.create(name='Python编程')
        self.assertTrue(category_cn.slug)  # 只验证slug存在，不验证具体内容

    def test_category_hierarchy(self):
        """测试分类层次结构"""
        parent = Category.objects.create(name='编程语言')
        child = Category.objects.create(name='Python', parent=parent)

        self.assertEqual(child.parent, parent)
        self.assertEqual(parent.category_set.first(), child)

    def test_category_str_method(self):
        """测试__str__方法"""
        category = Category.objects.create(name='测试分类')
        self.assertEqual(str(category), '测试分类')


class CategoryViewTest(TestCase):
    def setUp(self):
        self.client = Client()
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        self.category = Category.objects.create(
            name='测试分类',
            description='测试描述'
        )

    def test_category_list_view(self):
        """测试分类列表页面"""
        response = self.client.get(reverse('knowledge_core:category_list'))
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试分类')

    def test_category_detail_view(self):
        """测试分类详情页面"""
        response = self.client.get(
            reverse('knowledge_core:category_detail', args=[self.category.slug])
        )
        self.assertEqual(response.status_code, 200)
        self.assertContains(response, '测试分类')
        self.assertContains(response, '测试描述')

    def test_category_create_requires_login(self):
        """测试创建分类需要登录"""
        response = self.client.get(reverse('knowledge_core:category_create'))
        self.assertEqual(response.status_code, 302)  # 重定向到登录页面

    def test_category_create_authenticated(self):
        """测试登录用户可以访问创建分类页面"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.get(reverse('knowledge_core:category_create'))
        self.assertEqual(response.status_code, 200)

    def test_category_create_post(self):
        """测试创建分类POST请求"""
        self.client.login(username='testuser', password='testpass123')
        response = self.client.post(reverse('knowledge_core:category_create'), {
            'name': '新分类',
            'description': '新分类描述'
        })
        self.assertEqual(response.status_code, 302)  # 重定向到详情页面

        # 验证分类已创建
        category = Category.objects.get(name='新分类')
        self.assertEqual(category.description, '新分类描述')


class AuthenticationTest(TestCase):
    def setUp(self):
        self.client = Client()

    def test_login_page_accessible(self):
        """测试登录页面可访问"""
        response = self.client.get('/accounts/login/')
        self.assertEqual(response.status_code, 200)

    def test_signup_page_accessible(self):
        """测试注册页面可访问"""
        response = self.client.get(reverse('knowledge_core:signup'))
        self.assertEqual(response.status_code, 200)
