import markdown2
from django import template
from django.utils.safestring import mark_safe

register = template.Library()

@register.filter(name='markdown')
def markdown_format(text):
    # 添加Markdown扩展支持（表格、代码块等）
    extras = [
        "fenced-code-blocks", 
        "tables", 
        "code-friendly",
        "cuddled-lists",
        "footnotes",
        "header-ids"
    ]
    html = markdown2.markdown(text, extras=extras)
    return mark_safe(html)