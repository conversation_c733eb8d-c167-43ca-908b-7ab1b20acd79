{% extends "base.html" %}

{% block title %}注册{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-person-plus"></i> 用户注册
                    </h4>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- 用户名 -->
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="bi bi-person"></i> 用户名 <span class="text-danger">*</span>
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="{{ form.username.id_for_label }}" 
                                   name="{{ form.username.name }}" 
                                   value="{{ form.username.value|default:'' }}"
                                   required>
                            {% if form.username.help_text %}
                                <div class="form-text">{{ form.username.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- 密码 -->
                        <div class="mb-3">
                            <label for="{{ form.password1.id_for_label }}" class="form-label">
                                <i class="bi bi-lock"></i> 密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="{{ form.password1.id_for_label }}" 
                                   name="{{ form.password1.name }}"
                                   required>
                            {% if form.password1.help_text %}
                                <div class="form-text">{{ form.password1.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- 确认密码 -->
                        <div class="mb-3">
                            <label for="{{ form.password2.id_for_label }}" class="form-label">
                                <i class="bi bi-lock-fill"></i> 确认密码 <span class="text-danger">*</span>
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="{{ form.password2.id_for_label }}" 
                                   name="{{ form.password2.name }}"
                                   required>
                            {% if form.password2.help_text %}
                                <div class="form-text">{{ form.password2.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-person-plus"></i> 注册
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        已有账户？<a href="{% url 'login' %}">立即登录</a>
                    </small>
                </div>
            </div>

            <!-- 帮助信息 -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-info-circle"></i> 注册说明
                    </h6>
                    <ul class="mb-0 small text-muted">
                        <li>用户名必须唯一，建议使用英文字母和数字</li>
                        <li>密码至少8位，建议包含字母和数字</li>
                        <li>注册成功后将自动登录系统</li>
                        <li>注册后即可开始创建和管理知识条目</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
