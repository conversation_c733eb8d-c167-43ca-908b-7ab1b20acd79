from rest_framework import serializers
from .models import Word, Meaning, Tag


class MeaningSerializer(serializers.ModelSerializer):
    class Meta:
        model = Meaning
        fields = ['id', 'word', 'part_of_speech', 'translation']


class TagSerializer(serializers.ModelSerializer):
    class Meta:
        model = Tag
        fields = ['id', 'name']


class WordSerializer(serializers.ModelSerializer):
    meanings = MeaningSerializer(many=True, read_only=True)
    tags = TagSerializer(many=True, read_only=True)

    class Meta:
        model = Word
        fields = [
            'id', 'name',
            'phonetic_us', 'phonetic_uk',
            'audio_us', 'audio_uk',
            'past_tense', 'past_participle', 'plural_form',
            'example_sentences',
            'meanings', 'tags',
            'created_at', 'updated_at',
        ]
