from django.shortcuts import render, redirect
from django.contrib.auth import login
from django.contrib.auth.forms import UserCreationForm
from django.contrib import messages
from django.views.generic import CreateView
from django.urls import reverse_lazy


class SignUpView(CreateView):
    form_class = UserCreationForm
    template_name = 'registration/signup.html'
    success_url = reverse_lazy('knowledge_core:knowledge_entry_list')
    
    def form_valid(self, form):
        response = super().form_valid(form)
        # 自动登录新注册的用户
        login(self.request, self.object)
        messages.success(self.request, '注册成功！欢迎使用知识库系统！')
        return response
