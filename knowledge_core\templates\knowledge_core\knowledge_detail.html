{% extends "base.html" %}
{% load markdown_extras %}

{% block title %}{{ knowledge.title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-start mb-4">
    <div>
        <h1>{{ knowledge.title }}</h1>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{% url 'knowledge_core:knowledge_entry_list' %}">知识库</a>
                </li>
                {% if knowledge.category %}
                <li class="breadcrumb-item">
                    <a href="{% url 'knowledge_core:category_knowledges' knowledge.category.slug %}">{{ knowledge.category.name }}</a>
                </li>
                {% endif %}
                <li class="breadcrumb-item active" aria-current="page">{{ knowledge.title }}</li>
            </ol>
        </nav>
    </div>
    <div class="btn-group">
        <a href="{% url 'knowledge_core:knowledge_entry_list' %}" class="btn btn-sm btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> 返回列表
        </a>
        {% if user.is_authenticated and user == knowledge.created_by or user.is_superuser %}
        <a href="{% url 'knowledge_core:knowledge_edit' knowledge.slug %}" class="btn btn-sm btn-outline-primary">
            <i class="bi bi-pencil"></i> 编辑
        </a>
        <a href="{% url 'knowledge_core:knowledge_delete' knowledge.slug %}" class="btn btn-sm btn-outline-danger">
            <i class="bi bi-trash"></i> 删除
        </a>
        {% endif %}
    </div>
</div>

<div class="card mb-4">
    <div class="card-body">
        <!-- 元数据信息 -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="mb-2">
                    <span class="badge bg-primary me-2">
                        <i class="bi bi-folder"></i>
                        {% if knowledge.category %}
                            <a href="{% url 'knowledge_core:category_knowledges' knowledge.category.slug %}" class="text-white text-decoration-none">
                                {{ knowledge.category }}
                            </a>
                        {% else %}
                            无分类
                        {% endif %}
                    </span>
                    {% for tag in knowledge.tags.all %}
                    <span class="badge bg-secondary me-1">
                        <i class="bi bi-tag"></i>
                        <a href="{% url 'knowledge_core:tag_knowledges' tag.slug %}" class="text-white text-decoration-none">
                            {{ tag.name }}
                        </a>
                    </span>
                    {% endfor %}
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="text-muted small">
                    <div><i class="bi bi-person"></i> 创建者: {{ knowledge.created_by.username }}</div>
                    <div><i class="bi bi-calendar-plus"></i> 创建: {{ knowledge.created_at|date:"Y-m-d H:i" }}</div>
                    <div><i class="bi bi-calendar-check"></i> 更新: {{ knowledge.updated_at|date:"Y-m-d H:i" }}</div>
                </div>
            </div>
        </div>
        
        <div class="markdown-content">
            {{ knowledge.content|markdown }}
        </div>
    </div>
</div>

{% if related_knowledges %}
<div class="card">
    <div class="card-header">
        <h5>Related Articles</h5>
    </div>
    <div class="card-body">
        <div class="list-group">
            {% for related in related_knowledges %}
            <a href="{% url 'knowledge_core:knowledge_detail' related.slug %}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">{{ related.title }}</h6>
                    <small>{{ related.updated_at|timesince }} ago</small>
                </div>
                <small class="text-muted">{{ related.category.name }}</small>
            </a>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endblock %}