{% extends "base.html" %}
{% load markdown_extras %}

{% block title %}{{ knowledge.title }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>{{ knowledge.title }}</h1>
    <div>
        <a href="{% url 'knowledge_core:knowledge_edit' knowledge.slug %}" class="btn btn-sm btn-outline-primary">
            <i class="bi bi-pencil"></i> Edit
        </a>
        <a href="{% url 'knowledge_core:knowledge_delete' knowledge.slug %}" class="btn btn-sm btn-outline-danger">
            <i class="bi bi-trash"></i> Delete
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-body">
        <div class="d-flex justify-content-between mb-3">
            <div>
                <span class="badge bg-primary">
                    <i class="bi bi-folder"></i> 
                    {% if knowledge.category %}
                        <a href="{% url 'knowledge_core:category_knowledges' knowledge.category.slug %}" class="text-white">
                            {{ knowledge.category }}
                        </a>
                    {% else %}
                        无分类
                    {% endif %}
                </span>
                {% for tag in knowledge.tags.all %}
                <span class="badge bg-secondary ms-1">
                    <i class="bi bi-tag"></i> 
                    <a href="{% url 'knowledge_core:tag_knowledges' tag.slug %}" class="text-white">
                        {{ tag.name }}
                    </a>
                </span>
                {% endfor %}
            </div>
            <div>
                <small class="text-muted">
                    Updated: {{ knowledge.updated_at|date:"M d, Y H:i" }}
                </small>
            </div>
        </div>
        
        <div class="markdown-content">
            {{ knowledge.content|markdown }}
        </div>
    </div>
</div>

{% if related_knowledges %}
<div class="card">
    <div class="card-header">
        <h5>Related Articles</h5>
    </div>
    <div class="card-body">
        <div class="list-group">
            {% for related in related_knowledges %}
            <a href="{% url 'knowledge_core:knowledge_detail' related.slug %}" class="list-group-item list-group-item-action">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">{{ related.title }}</h6>
                    <small>{{ related.updated_at|timesince }} ago</small>
                </div>
                <small class="text-muted">{{ related.category.name }}</small>
            </a>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}
{% endblock %}