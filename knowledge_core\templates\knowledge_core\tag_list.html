{% extends "base.html" %}

{% block title %}标签管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-tags"></i> 标签管理</h1>
        <a href="{% url 'knowledge_core:tag_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 新建标签
        </a>
    </div>

    {% if tags %}
        <div class="row">
            {% for tag in tags %}
            <div class="col-md-6 col-lg-4 col-xl-3 mb-3">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="bi bi-tag"></i> 
                            <a href="{% url 'knowledge_core:tag_detail' tag.slug %}" class="text-decoration-none">
                                {{ tag.name }}
                            </a>
                        </h6>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-primary">
                                {{ tag.knowledge_count }} 个知识条目
                            </span>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'knowledge_core:tag_detail' tag.slug %}" 
                                   class="btn btn-outline-primary" title="查看">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'knowledge_core:tag_edit' tag.slug %}" 
                                   class="btn btn-outline-secondary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'knowledge_core:tag_delete' tag.slug %}" 
                                   class="btn btn-outline-danger" title="删除">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if is_paginated %}
        <nav aria-label="标签分页">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">首页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-tags" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3 text-muted">暂无标签</h3>
            <p class="text-muted">开始创建您的第一个标签吧！</p>
            <a href="{% url 'knowledge_core:tag_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建标签
            </a>
        </div>
    {% endif %}

    <!-- 标签云 -->
    {% if tags %}
    <div class="mt-5">
        <h4><i class="bi bi-cloud"></i> 标签云</h4>
        <div class="card">
            <div class="card-body">
                {% for tag in tags %}
                    {% if tag.knowledge_count > 0 %}
                    <a href="{% url 'knowledge_core:tag_knowledges' tag.slug %}" 
                       class="badge me-2 mb-2 text-decoration-none
                       {% if tag.knowledge_count >= 10 %}bg-danger fs-5{% elif tag.knowledge_count >= 5 %}bg-warning fs-6{% elif tag.knowledge_count >= 2 %}bg-info{% else %}bg-secondary{% endif %}"
                       title="{{ tag.knowledge_count }} 个知识条目">
                        {{ tag.name }}
                    </a>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}

    <!-- 统计信息 -->
    <div class="mt-4">
        <div class="row text-center">
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ page_obj.paginator.count }}</h5>
                        <p class="card-text text-muted">总标签数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            {% for tag in tags %}
                                {% if tag.knowledge_count > 0 %}{{ forloop.counter0|add:1 }}{% endif %}
                            {% empty %}0{% endfor %}
                        </h5>
                        <p class="card-text text-muted">使用中的标签</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ tags|length }}</h5>
                        <p class="card-text text-muted">当前页面</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
