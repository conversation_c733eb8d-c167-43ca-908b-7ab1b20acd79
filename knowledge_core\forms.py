from django import forms
from .models import KnowledgeEntry, Category, Tag

class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['name', 'parent', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'parent': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': '分类描述（可选）...'
            }),
        }
        labels = {
            'name': '分类名称',
            'parent': '父分类',
            'description': '描述',
        }

class KnowledgeEntryForm(forms.ModelForm):
    class Meta:
        model = KnowledgeEntry
        fields = ['title', 'content', 'tags', 'category']
        widgets = {
            'content': forms.Textarea(attrs={
                'rows': 15, 
                'class': 'form-control markdown-editor',
                'placeholder': 'Write your content in Markdown format...',
                'id': 'markdown-editor'
            }),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'tags': forms.SelectMultiple(attrs={'class': 'form-select'}),
        }
        # labels 用于自定义字段的显示名称
        labels = {
            'title': '标题',
            'content': '内容',
            'tags': '标签',
            'category': '分类',
        }