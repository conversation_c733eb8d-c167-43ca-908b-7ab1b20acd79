from django import forms
from .models import KnowledgeEntry, Category, Tag

class CategoryForm(forms.ModelForm):
    class Meta:
        model = Category
        fields = ['name', 'parent', 'description']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'parent': forms.Select(attrs={'class': 'form-select'}),
            'description': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': '分类描述（可选）...'
            }),
        }
        labels = {
            'name': '分类名称',
            'parent': '父分类',
            'description': '描述',
        }

class KnowledgeEntryForm(forms.ModelForm):
    class Meta:
        model = KnowledgeEntry
        fields = ['title', 'content', 'tags', 'category']
        widgets = {
            'content': forms.Textarea(attrs={
                'rows': 15,
                'class': 'form-control markdown-editor',
                'placeholder': '在这里输入Markdown内容...\n\n支持的语法：\n# 标题\n**粗体** *斜体*\n- 列表项\n```代码块```\n[链接](URL)',
                'id': 'markdown-editor'
            }),
            'title': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入知识条目标题...'
            }),
            'category': forms.Select(attrs={
                'class': 'form-select',
                'data-placeholder': '选择分类（可选）'
            }),
            'tags': forms.SelectMultiple(attrs={
                'class': 'form-select',
                'data-placeholder': '选择标签（可选，可多选）',
                'size': '5'
            }),
        }
        labels = {
            'title': '标题',
            'content': '内容',
            'tags': '标签',
            'category': '分类',
        }
        help_texts = {
            'title': '为您的知识条目起一个清晰、描述性的标题',
            'content': '使用Markdown语法编写内容，支持代码块、表格、链接等',
            'category': '选择合适的分类有助于组织和查找知识',
            'tags': '添加相关标签，便于搜索和关联',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 为分类添加空选项
        self.fields['category'].empty_label = "选择分类（可选）"
        # 设置标签字段为非必填
        self.fields['tags'].required = False
        self.fields['category'].required = False