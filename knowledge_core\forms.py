from django import forms
from .models import KnowledgeEntry, Category, Tag

class KnowledgeEntryForm(forms.ModelForm):
    class Meta:
        model = KnowledgeEntry
        fields = ['title', 'content', 'tags', 'category']
        widgets = {
            'content': forms.Textarea(attrs={
                'rows': 15, 
                'class': 'form-control markdown-editor',
                'placeholder': 'Write your content in Markdown format...',
                'id': 'markdown-editor'
            }),
            'title': forms.TextInput(attrs={'class': 'form-control'}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'tags': forms.SelectMultiple(attrs={'class': 'form-select'}),
        }
        # labels 用于自定义字段的显示名称
        labels = {
            'title': '标题',
            'content': '内容',
            'tags': '标签',
            'category': '分类',
        }