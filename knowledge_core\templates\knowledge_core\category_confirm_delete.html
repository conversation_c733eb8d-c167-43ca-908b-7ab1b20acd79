{% extends "base.html" %}

{% block title %}删除分类 - {{ category.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> 确认删除分类
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <strong>警告：</strong> 此操作不可撤销！
                    </div>

                    <p>您确定要删除分类 <strong>"{{ category.name }}"</strong> 吗？</p>

                    {% if category.description %}
                    <div class="mb-3">
                        <h6>分类描述：</h6>
                        <p class="text-muted">{{ category.description }}</p>
                    </div>
                    {% endif %}

                    <!-- 影响分析 -->
                    <div class="mb-4">
                        <h6>删除影响：</h6>
                        <ul class="list-unstyled">
                            {% with knowledge_count=category.knowledgeentry_set.count subcategory_count=category.category_set.count %}
                            <li class="d-flex justify-content-between">
                                <span>关联的知识条目：</span>
                                <span class="{% if knowledge_count > 0 %}text-warning{% else %}text-muted{% endif %}">
                                    {{ knowledge_count }} 个
                                </span>
                            </li>
                            <li class="d-flex justify-content-between">
                                <span>子分类：</span>
                                <span class="{% if subcategory_count > 0 %}text-warning{% else %}text-muted{% endif %}">
                                    {{ subcategory_count }} 个
                                </span>
                            </li>
                            {% endwith %}
                        </ul>

                        {% if category.knowledgeentry_set.count > 0 %}
                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle"></i>
                            <strong>注意：</strong> 删除此分类后，关联的知识条目将变为"无分类"状态。
                        </div>
                        {% endif %}

                        {% if category.category_set.count > 0 %}
                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle"></i>
                            <strong>注意：</strong> 删除此分类后，子分类将变为顶级分类。
                        </div>
                        {% endif %}
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'knowledge_core:category_detail' category.slug %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> 确认删除
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 替代方案 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-lightbulb"></i> 替代方案</h6>
                    <p class="card-text small text-muted">
                        如果您只是想重新组织分类结构，可以考虑：
                    </p>
                    <ul class="small text-muted mb-0">
                        <li>编辑分类名称或描述</li>
                        <li>将知识条目移动到其他分类</li>
                        <li>重新设置分类的父子关系</li>
                    </ul>
                    <div class="mt-2">
                        <a href="{% url 'knowledge_core:category_edit' category.slug %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil"></i> 编辑分类
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
