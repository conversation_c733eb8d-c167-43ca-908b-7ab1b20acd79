# Generated by Django 5.2.4 on 2025-07-03 07:54

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, unique=True)),
            ],
        ),
        migrations.CreateModel(
            name='Word',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('phonetic_us', models.CharField(blank=True, max_length=100)),
                ('phonetic_uk', models.CharField(blank=True, max_length=100)),
                ('audio_us', models.URLField(blank=True)),
                ('audio_uk', models.URLField(blank=True)),
                ('past_tense', models.CharField(blank=True, max_length=100)),
                ('past_participle', models.CharField(blank=True, max_length=100)),
                ('plural_form', models.CharField(blank=True, max_length=100)),
                ('example_sentences', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('tags', models.ManyToManyField(blank=True, to='english.tag')),
            ],
        ),
        migrations.CreateModel(
            name='Meaning',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('part_of_speech', models.CharField(blank=True, choices=[('n', '名词 (Noun)'), ('v', '动词 (Verb)'), ('adj', '形容词 (Adjective)'), ('adv', '副词 (Adverb)'), ('prep', '介词 (Preposition)'), ('conj', '连词 (Conjunction)'), ('pron', '代词 (Pronoun)'), ('intj', '感叹词 (Interjection)'), ('art', '冠词 (Article)'), ('phr', '短语 (Phrase)'), ('idiom', '习语 (Idiom)'), ('other', '其他 (Other)')], max_length=10, verbose_name='词性')),
                ('translation', models.CharField(max_length=255)),
                ('word', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meanings', to='english.word')),
            ],
        ),
    ]
