{% extends "base.html" %}

{% block title %}{{ tag.name }} - 标签详情{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 标签头部信息 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1><i class="bi bi-tag"></i> {{ tag.name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'knowledge_core:tag_list' %}">所有标签</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">{{ tag.name }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{% url 'knowledge_core:tag_edit' tag.slug %}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i> 编辑
            </a>
            <a href="{% url 'knowledge_core:tag_delete' tag.slug %}" class="btn btn-outline-danger">
                <i class="bi bi-trash"></i> 删除
            </a>
        </div>
    </div>

    <div class="row">
        <!-- 主要内容区域 -->
        <div class="col-lg-8">
            <!-- 知识条目列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-journal-text"></i> 相关知识条目</h5>
                    <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus"></i> 新建知识条目
                    </a>
                </div>
                <div class="card-body">
                    {% if knowledges %}
                        {% for knowledge in knowledges %}
                        <div class="border-bottom pb-3 mb-3">
                            <h6>
                                <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" class="text-decoration-none">
                                    {{ knowledge.title }}
                                </a>
                            </h6>
                            
                            {% if knowledge.content %}
                            <p class="text-muted small mb-2">
                                {{ knowledge.content|truncatewords:15|striptags }}
                            </p>
                            {% endif %}
                            
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% if knowledge.category %}
                                    <span class="badge bg-primary me-1">
                                        <i class="bi bi-folder"></i> {{ knowledge.category.name }}
                                    </span>
                                    {% endif %}
                                    {% for other_tag in knowledge.tags.all %}
                                        {% if other_tag != tag %}
                                        <span class="badge bg-secondary me-1">{{ other_tag.name }}</span>
                                        {% endif %}
                                    {% endfor %}
                                </div>
                                <div class="text-end">
                                    <small class="text-muted d-block">
                                        <i class="bi bi-person"></i> {{ knowledge.created_by.username }}
                                    </small>
                                    <small class="text-muted">
                                        <i class="bi bi-calendar"></i> {{ knowledge.updated_at|date:"Y-m-d H:i" }}
                                    </small>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <!-- 查看更多链接 -->
                        <div class="text-center mt-3">
                            <a href="{% url 'knowledge_core:tag_knowledges' tag.slug %}" class="btn btn-outline-primary">
                                <i class="bi bi-eye"></i> 查看所有相关知识条目
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-journal-x" style="font-size: 3rem; color: #6c757d;"></i>
                            <h6 class="mt-2 text-muted">该标签下暂无知识条目</h6>
                            <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary mt-2">
                                <i class="bi bi-plus"></i> 创建第一个知识条目
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">标签统计</h6>
                    <ul class="list-unstyled">
                        <li class="d-flex justify-content-between">
                            <span>知识条目数量:</span>
                            <strong>{{ knowledges|length }}</strong>
                        </li>
                        <li class="d-flex justify-content-between">
                            <span>标签名称:</span>
                            <strong>{{ tag.name }}</strong>
                        </li>
                        <li class="d-flex justify-content-between">
                            <span>URL标识:</span>
                            <code>{{ tag.slug }}</code>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 相关标签 -->
            {% if knowledges %}
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">相关标签</h6>
                    {% regroup knowledges by tags.all as knowledge_tags %}
                    <div class="d-flex flex-wrap">
                        {% for knowledge in knowledges %}
                            {% for other_tag in knowledge.tags.all %}
                                {% if other_tag != tag %}
                                <a href="{% url 'knowledge_core:tag_detail' other_tag.slug %}" 
                                   class="badge bg-secondary me-1 mb-1 text-decoration-none">
                                    {{ other_tag.name }}
                                </a>
                                {% endif %}
                            {% endfor %}
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 操作提示 -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-info-circle"></i> 使用提示</h6>
                    <ul class="mb-0 small text-muted">
                        <li>标签用于对知识条目进行分类和标记</li>
                        <li>一个知识条目可以有多个标签</li>
                        <li>通过标签可以快速找到相关内容</li>
                        <li>删除标签不会删除知识条目</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
