# Generated by Django 5.2.4 on 2025-07-17 02:27

from django.db import migrations
from slugify import slugify


def fix_slugs(apps, schema_editor):
    """修复现有记录的slug，使用支持中文的slugify"""
    Word = apps.get_model('english', 'Word')
    Tag = apps.get_model('english', 'Tag')

    # 修复Word的slug
    for word in Word.objects.all():
        if not word.slug:
            base_slug = slugify(word.name)
            if not base_slug:  # 如果slugify后为空，使用word的id
                base_slug = f"word-{word.id}"
            slug = base_slug
            num = 1
            while Word.objects.filter(slug=slug).exclude(pk=word.pk).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            word.slug = slug
            word.save()

    # 修复Tag的slug
    for tag in Tag.objects.all():
        if not tag.slug:
            base_slug = slugify(tag.name)
            if not base_slug:  # 如果slugify后为空，使用tag的id
                base_slug = f"tag-{tag.id}"
            slug = base_slug
            num = 1
            while Tag.objects.filter(slug=slug).exclude(pk=tag.pk).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            tag.slug = slug
            tag.save()


def reverse_fix_slugs(apps, schema_editor):
    """反向操作：清空slug字段"""
    Word = apps.get_model('english', 'Word')
    Tag = apps.get_model('english', 'Tag')

    Word.objects.update(slug='')
    Tag.objects.update(slug='')


class Migration(migrations.Migration):

    dependencies = [
        ('english', '0003_alter_tag_options_alter_word_options_tag_slug_and_more'),
    ]

    operations = [
        migrations.RunPython(fix_slugs, reverse_fix_slugs),
    ]
