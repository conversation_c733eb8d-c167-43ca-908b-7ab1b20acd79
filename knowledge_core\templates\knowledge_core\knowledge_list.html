{% extends "base.html" %}

{% block title %}知识库{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 页面标题和操作 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-journal-text"></i> 知识库</h1>
        {% if user.is_authenticated %}
        <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 新建知识条目
        </a>
        {% endif %}
    </div>

    <!-- 筛选和排序 -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="category" class="form-label">分类</label>
                    <select name="category" id="category" class="form-select">
                        <option value="">所有分类</option>
                        {% for category in categories %}
                        <option value="{{ category.slug }}" {% if current_category == category.slug %}selected{% endif %}>
                            {{ category.name }} ({{ category.knowledge_count }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="tag" class="form-label">标签</label>
                    <select name="tag" id="tag" class="form-select">
                        <option value="">所有标签</option>
                        {% for tag in tags %}
                        <option value="{{ tag.slug }}" {% if current_tag == tag.slug %}selected{% endif %}>
                            {{ tag.name }} ({{ tag.knowledge_count }})
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="author" class="form-label">创建者</label>
                    <input type="text" name="author" id="author" class="form-control" 
                           value="{{ current_author }}" placeholder="用户名">
                </div>
                <div class="col-md-2">
                    <label for="sort" class="form-label">排序</label>
                    <select name="sort" id="sort" class="form-select">
                        <option value="-updated_at" {% if current_sort == '-updated_at' %}selected{% endif %}>最新更新</option>
                        <option value="updated_at" {% if current_sort == 'updated_at' %}selected{% endif %}>最早更新</option>
                        <option value="-created_at" {% if current_sort == '-created_at' %}selected{% endif %}>最新创建</option>
                        <option value="created_at" {% if current_sort == 'created_at' %}selected{% endif %}>最早创建</option>
                        <option value="title" {% if current_sort == 'title' %}selected{% endif %}>标题A-Z</option>
                        <option value="-title" {% if current_sort == '-title' %}selected{% endif %}>标题Z-A</option>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-outline-primary me-2">
                        <i class="bi bi-funnel"></i> 筛选
                    </button>
                    <a href="{% url 'knowledge_core:knowledge_entry_list' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-clockwise"></i>
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 知识条目列表 -->
    {% if knowledges %}
        <div class="row">
            {% for knowledge in knowledges %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" class="text-decoration-none">
                                {{ knowledge.title }}
                            </a>
                        </h5>
                        
                        {% if knowledge.content %}
                        <p class="card-text text-muted">
                            {{ knowledge.content|truncatewords:20|striptags }}
                        </p>
                        {% endif %}
                        
                        <div class="mb-3">
                            {% if knowledge.category %}
                            <span class="badge bg-primary me-1">
                                <i class="bi bi-folder"></i> {{ knowledge.category.name }}
                            </span>
                            {% endif %}
                            {% for tag in knowledge.tags.all|slice:":3" %}
                            <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                            {% endfor %}
                            {% if knowledge.tags.count > 3 %}
                            <span class="badge bg-light text-dark">+{{ knowledge.tags.count|add:"-3" }}</span>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                <i class="bi bi-person"></i> {{ knowledge.created_by.username }}<br>
                                <i class="bi bi-calendar"></i> {{ knowledge.updated_at|date:"Y-m-d" }}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" 
                                   class="btn btn-outline-primary" title="查看">
                                    <i class="bi bi-eye"></i>
                                </a>
                                {% if user.is_authenticated and user == knowledge.created_by or user.is_superuser %}
                                <a href="{% url 'knowledge_core:knowledge_edit' knowledge.slug %}" 
                                   class="btn btn-outline-secondary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if is_paginated %}
        <nav aria-label="知识条目分页">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page=1">首页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.previous_page_number }}">上一页</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.next_page_number }}">下一页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?{% for key, value in request.GET.items %}{% if key != 'page' %}{{ key }}={{ value }}&{% endif %}{% endfor %}page={{ page_obj.paginator.num_pages }}">末页</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-journal-x" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3 text-muted">暂无知识条目</h3>
            <p class="text-muted">
                {% if request.GET %}
                    没有找到符合条件的知识条目，请尝试调整筛选条件。
                {% else %}
                    开始创建您的第一个知识条目吧！
                {% endif %}
            </p>
            {% if user.is_authenticated %}
            <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建知识条目
            </a>
            {% endif %}
        </div>
    {% endif %}

    <!-- 统计信息 -->
    <div class="mt-5">
        <div class="row text-center">
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ page_obj.paginator.count }}</h5>
                        <p class="card-text text-muted">知识条目</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ categories.count }}</h5>
                        <p class="card-text text-muted">分类</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ tags.count }}</h5>
                        <p class="card-text text-muted">标签</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">{{ knowledges|length }}</h5>
                        <p class="card-text text-muted">当前页面</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
