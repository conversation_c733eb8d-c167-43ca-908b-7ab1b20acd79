# Generated by Django 5.2.4 on 2025-07-17 02:26

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('english', '0002_alter_meaning_translation'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='tag',
            options={'ordering': ['name']},
        ),
        migrations.AlterModelOptions(
            name='word',
            options={'ordering': ['name'], 'verbose_name': '单词', 'verbose_name_plural': '单词'},
        ),
        migrations.AddField(
            model_name='tag',
            name='slug',
            field=models.SlugField(blank=True, max_length=60, unique=True),
        ),
        migrations.AddField(
            model_name='word',
            name='author',
            field=models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='创建者'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='word',
            name='slug',
            field=models.SlugField(blank=True, max_length=120, unique=True),
        ),
        migrations.AlterField(
            model_name='word',
            name='audio_uk',
            field=models.URLField(blank=True, verbose_name='英式发音'),
        ),
        migrations.AlterField(
            model_name='word',
            name='audio_us',
            field=models.URLField(blank=True, verbose_name='美式发音'),
        ),
        migrations.AlterField(
            model_name='word',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='word',
            name='example_sentences',
            field=models.TextField(blank=True, verbose_name='例句'),
        ),
        migrations.AlterField(
            model_name='word',
            name='name',
            field=models.CharField(max_length=100, unique=True, verbose_name='单词'),
        ),
        migrations.AlterField(
            model_name='word',
            name='past_participle',
            field=models.CharField(blank=True, max_length=100, verbose_name='过去分词'),
        ),
        migrations.AlterField(
            model_name='word',
            name='past_tense',
            field=models.CharField(blank=True, max_length=100, verbose_name='过去式'),
        ),
        migrations.AlterField(
            model_name='word',
            name='phonetic_uk',
            field=models.CharField(blank=True, max_length=100, verbose_name='英式音标'),
        ),
        migrations.AlterField(
            model_name='word',
            name='phonetic_us',
            field=models.CharField(blank=True, max_length=100, verbose_name='美式音标'),
        ),
        migrations.AlterField(
            model_name='word',
            name='plural_form',
            field=models.CharField(blank=True, max_length=100, verbose_name='复数形式'),
        ),
        migrations.AlterField(
            model_name='word',
            name='tags',
            field=models.ManyToManyField(blank=True, to='english.tag', verbose_name='标签'),
        ),
        migrations.AlterField(
            model_name='word',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='更新时间'),
        ),
    ]
