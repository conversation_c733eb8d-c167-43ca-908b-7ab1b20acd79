{% extends "base.html" %}

{% block title %}删除标签 - {{ tag.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> 确认删除标签
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <strong>警告：</strong> 此操作不可撤销！
                    </div>

                    <p>您确定要删除标签 <strong>"{{ tag.name }}"</strong> 吗？</p>

                    <!-- 影响分析 -->
                    <div class="mb-4">
                        <h6>删除影响：</h6>
                        <ul class="list-unstyled">
                            {% with knowledge_count=tag.knowledgeentry_set.count %}
                            <li class="d-flex justify-content-between">
                                <span>关联的知识条目：</span>
                                <span class="{% if knowledge_count > 0 %}text-warning{% else %}text-muted{% endif %}">
                                    {{ knowledge_count }} 个
                                </span>
                            </li>
                            {% endwith %}
                        </ul>

                        {% if tag.knowledgeentry_set.count > 0 %}
                        <div class="alert alert-info" role="alert">
                            <i class="bi bi-info-circle"></i>
                            <strong>注意：</strong> 删除此标签后，相关知识条目将失去此标签，但知识条目本身不会被删除。
                        </div>
                        
                        <!-- 显示受影响的知识条目 -->
                        <div class="mt-3">
                            <h6>受影响的知识条目：</h6>
                            <div class="border rounded p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                                {% for knowledge in tag.knowledgeentry_set.all|slice:":10" %}
                                <div class="mb-1">
                                    <i class="bi bi-journal-text"></i>
                                    <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" 
                                       class="text-decoration-none" target="_blank">
                                        {{ knowledge.title }}
                                    </a>
                                </div>
                                {% endfor %}
                                {% if tag.knowledgeentry_set.count > 10 %}
                                <div class="text-muted small">
                                    ... 还有 {{ tag.knowledgeentry_set.count|add:"-10" }} 个知识条目
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'knowledge_core:tag_detail' tag.slug %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> 确认删除
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 替代方案 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-lightbulb"></i> 替代方案</h6>
                    <p class="card-text small text-muted">
                        如果您只是想修改标签，可以考虑：
                    </p>
                    <ul class="small text-muted mb-0">
                        <li>编辑标签名称</li>
                        <li>将知识条目的标签更改为其他标签</li>
                        <li>创建新标签来替代当前标签</li>
                    </ul>
                    <div class="mt-2">
                        <a href="{% url 'knowledge_core:tag_edit' tag.slug %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil"></i> 编辑标签
                        </a>
                    </div>
                </div>
            </div>

            <!-- 标签信息 -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-info-circle"></i> 标签信息</h6>
                    <ul class="list-unstyled small mb-0">
                        <li><strong>标签名称：</strong> {{ tag.name }}</li>
                        <li><strong>URL标识：</strong> {{ tag.slug }}</li>
                        <li><strong>关联知识条目：</strong> {{ tag.knowledgeentry_set.count }} 个</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
