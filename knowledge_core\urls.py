from django.urls import path
from . import views

app_name = 'knowledge_core'

urlpatterns = [
    path('index/', views.index, name='index'),
    path('', views.KnowledgeEntryListView.as_view(), name='knowledge_entry_list'),

    # Knowledge Entry URLs
    path('knowledge/new/', views.KnowledgeEntryCreateView.as_view(), name='knowledge_create'),
    path('knowledge/<slug:slug>/', views.KnowledgeEntryDetailView.as_view(), name='knowledge_detail'),
    path('knowledge/<slug:slug>/edit/', views.KnowledgeEntryUpdateView.as_view(), name='knowledge_edit'),
    path('knowledge/<slug:slug>/delete/', views.KnowledgeEntryDeleteView.as_view(), name='knowledge_delete'),

    # Category URLs
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('category/new/', views.CategoryCreateView.as_view(), name='category_create'),
    path('category/<slug:slug>/', views.CategoryDetailView.as_view(), name='category_detail'),
    path('category/<slug:slug>/edit/', views.CategoryUpdateView.as_view(), name='category_edit'),
    path('category/<slug:slug>/delete/', views.CategoryDeleteView.as_view(), name='category_delete'),
    path('category/<slug:slug>/knowledges/', views.category_knowledges, name='category_knowledges'),

    # Tag URLs
    path('tag/<slug:slug>/', views.tag_knowledges, name='tag_knowledges'),

    # Search
    path('search/', views.search, name='search'),
]