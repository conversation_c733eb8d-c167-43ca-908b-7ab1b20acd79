from django.urls import path
from . import views
from .auth_views import SignUpView

app_name = 'knowledge_core'

urlpatterns = [
    path('index/', views.index, name='index'),
    path('', views.KnowledgeEntryListView.as_view(), name='knowledge_entry_list'),

    # Authentication URLs
    path('signup/', SignUpView.as_view(), name='signup'),

    # Knowledge Entry URLs
    path('knowledge/new/', views.KnowledgeEntryCreateView.as_view(), name='knowledge_create'),
    path('knowledge/<str:slug>/', views.KnowledgeEntryDetailView.as_view(), name='knowledge_detail'),
    path('knowledge/<str:slug>/edit/', views.KnowledgeEntryUpdateView.as_view(), name='knowledge_edit'),
    path('knowledge/<str:slug>/delete/', views.KnowledgeEntryDeleteView.as_view(), name='knowledge_delete'),

    # Category URLs
    path('categories/', views.CategoryListView.as_view(), name='category_list'),
    path('category/new/', views.CategoryCreateView.as_view(), name='category_create'),
    path('category/<str:slug>/', views.CategoryDetailView.as_view(), name='category_detail'),
    path('category/<str:slug>/edit/', views.CategoryUpdateView.as_view(), name='category_edit'),
    path('category/<str:slug>/delete/', views.CategoryDeleteView.as_view(), name='category_delete'),
    path('category/<str:slug>/knowledges/', views.category_knowledges, name='category_knowledges'),

    # Tag URLs
    path('tags/', views.TagListView.as_view(), name='tag_list'),
    path('tag/new/', views.TagCreateView.as_view(), name='tag_create'),
    path('tag/<str:slug>/', views.TagDetailView.as_view(), name='tag_detail'),
    path('tag/<str:slug>/edit/', views.TagUpdateView.as_view(), name='tag_edit'),
    path('tag/<str:slug>/delete/', views.TagDeleteView.as_view(), name='tag_delete'),
    path('tag/<str:slug>/knowledges/', views.tag_knowledges, name='tag_knowledges'),

    # Search
    path('search/', views.search, name='search'),

    # Demo
    path('markdown-demo/', views.markdown_demo, name='markdown_demo'),
]