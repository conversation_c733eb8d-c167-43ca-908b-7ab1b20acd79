from django.urls import path
from . import views

app_name = 'knowledge_core'

urlpatterns = [
    path('index/', views.index, name='index'),
    path('', views.KnowledgeEntryListView.as_view(), name='knowledge_entry_list'),
    path('knowledge/new/', views.KnowledgeEntryCreateView.as_view(), name='knowledge_create'),
    path('knowledge/<slug:slug>/', views.KnowledgeEntryDetailView.as_view(), name='knowledge_detail'),
    path('knowledge/<slug:slug>/edit/', views.KnowledgeEntryUpdateView.as_view(), name='knowledge_edit'),
    path('knowledge/<slug:slug>/delete/', views.KnowledgeEntryDeleteView.as_view(), name='knowledge_delete'),
    path('category/<slug:slug>/', views.category_knowledges, name='category_knowledges'),
    path('tag/<slug:slug>/', views.tag_knowledges, name='tag_knowledges'),
    path('search/', views.search, name='search'),  # 确保这行存在
]