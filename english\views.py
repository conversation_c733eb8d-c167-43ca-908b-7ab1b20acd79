from django.shortcuts import render, get_object_or_404
from rest_framework import viewsets
from .models import Word, Meaning, Tag
from .serializers import WordSerializer, MeaningSerializer, TagSerializer
from .forms import WordForm
from django.views.generic import ListView
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.db.models import Q
from django.urls import reverse_lazy


class WordViewSet(viewsets.ModelViewSet):
    queryset = Word.objects.all()
    serializer_class = WordSerializer


class MeaningViewSet(viewsets.ModelViewSet):
    queryset = Meaning.objects.all()
    serializer_class = MeaningSerializer


class TagViewSet(viewsets.ModelViewSet):
    queryset = Tag.objects.all()
    serializer_class = TagSerializer


class WordListView(LoginRequiredMixin, ListView):
    model = Word
    template_name = 'english/word_list.html'
    context_object_name = 'words'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Word.objects.all()
        
        # 过滤条件
        tag = self.request.GET.get('tag')
        if tag:
            queryset = queryset.filter(tags__slug=tag)

        return queryset.order_by('name')


class WordDetailView(LoginRequiredMixin, DetailView):
    model = Word
    template_name = 'english/word_detail.html'
    context_object_name = 'word'
    
    def get_object(self):
        return get_object_or_404(Word, slug=self.kwargs['slug'], author=self.request.user)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class WordCreateView(LoginRequiredMixin, CreateView):
    model = Word
    form_class = WordForm
    template_name = 'english/word_form.html'
    
    def form_valid(self, form):
        form.instance.author = self.request.user
        messages.success(self.request, '单词创建成功！')
        return super().form_valid(form)
    
    def get_success_url(self):
        return self.object.get_absolute_url()


class WordUpdateView(LoginRequiredMixin, UpdateView):
    model = Word
    form_class = WordForm
    template_name = 'english/word_form.html'
    
    def get_object(self):
        return get_object_or_404(Word, slug=self.kwargs['slug'], author=self.request.user)
    
    def form_valid(self, form):
        messages.success(self.request, '单词更新成功！')
        return super().form_valid(form)
    
    def get_success_url(self):
        return self.object.get_absolute_url()

class WordDeleteView(LoginRequiredMixin, DeleteView):
    model = Word
    template_name = 'english/word_confirm_delete.html'
    success_url = reverse_lazy('word_list')
    
    def get_object(self):
        return get_object_or_404(Word, slug=self.kwargs['slug'], author=self.request.user)
    
    def delete(self, request, *args, **kwargs):
        messages.success(request, '单词已删除！')
        return super().delete(request, *args, **kwargs)