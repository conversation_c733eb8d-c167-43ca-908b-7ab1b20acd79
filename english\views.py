from django.shortcuts import render, get_object_or_404, redirect
from rest_framework import viewsets
from .models import Word, Meaning, Tag
from .serializers import WordSerializer, MeaningSerializer, TagSerializer
from .forms import WordForm
from django.views.generic import ListView
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.db.models import Q
from django.urls import reverse_lazy


class WordViewSet(viewsets.ModelViewSet):
    queryset = Word.objects.all()
    serializer_class = WordSerializer


class MeaningViewSet(viewsets.ModelViewSet):
    queryset = Meaning.objects.all()
    serializer_class = MeaningSerializer


class TagViewSet(viewsets.ModelViewSet):
    queryset = Tag.objects.all()
    serializer_class = TagSerializer


class WordListView(LoginRequiredMixin, ListView):
    model = Word
    template_name = 'english/word_list.html'
    context_object_name = 'words'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = Word.objects.select_related('author').prefetch_related('tags', 'meanings')

        # 搜索功能
        search = self.request.GET.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(meanings__translation__icontains=search)
            ).distinct()

        # 标签过滤
        tag = self.request.GET.get('tag')
        if tag:
            queryset = queryset.filter(tags__slug=tag)

        return queryset.order_by('name')


class WordDetailView(LoginRequiredMixin, DetailView):
    model = Word
    template_name = 'english/word_detail.html'
    context_object_name = 'word'
    
    def get_object(self):
        return get_object_or_404(Word, slug=self.kwargs['slug'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        return context


class WordCreateView(LoginRequiredMixin, CreateView):
    model = Word
    form_class = WordForm
    template_name = 'english/word_form.html'
    
    def form_valid(self, form):
        form.instance.author = self.request.user
        messages.success(self.request, '单词创建成功！')
        return super().form_valid(form)
    
    def get_success_url(self):
        return self.object.get_absolute_url()


class WordUpdateView(LoginRequiredMixin, UpdateView):
    model = Word
    form_class = WordForm
    template_name = 'english/word_form.html'
    
    def get_object(self):
        obj = get_object_or_404(Word, slug=self.kwargs['slug'])
        # 检查权限：只有创建者或超级用户可以编辑
        if obj.author != self.request.user and not self.request.user.is_superuser:
            messages.error(self.request, '您没有权限编辑此单词。')
            return redirect('english:word_detail', slug=obj.slug)
        return obj
    
    def form_valid(self, form):
        messages.success(self.request, '单词更新成功！')
        return super().form_valid(form)
    
    def get_success_url(self):
        return self.object.get_absolute_url()

class WordDeleteView(LoginRequiredMixin, DeleteView):
    model = Word
    template_name = 'english/word_confirm_delete.html'
    success_url = reverse_lazy('english:word_list')

    def get_object(self):
        obj = get_object_or_404(Word, slug=self.kwargs['slug'])
        # 检查权限：只有创建者或超级用户可以删除
        if obj.author != self.request.user and not self.request.user.is_superuser:
            messages.error(self.request, '您没有权限删除此单词。')
            return redirect('english:word_detail', slug=obj.slug)
        return obj

    def delete(self, request, *args, **kwargs):
        messages.success(request, '单词已删除！')
        return super().delete(request, *args, **kwargs)