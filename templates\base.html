<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personal Knowledge Base - {% block title %}{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .sidebar {
            background-color: #e9ecef;
            border-right: 1px solid #dee2e6;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            padding-top: 20px;
        }
        .main-content {
            margin-left: 280px;
            padding: 20px;
        }
        .article-card {
            transition: transform 0.2s;
        }
        .article-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        .markdown-content table {
            width: 100%;
            margin-bottom: 1rem;
            border-collapse: collapse;
        }
        .markdown-content table th,
        .markdown-content table td {
            padding: 0.75rem;
            vertical-align: top;
            border: 1px solid #dee2e6;
        }
        .markdown-content table thead th {
            vertical-align: bottom;
            border-bottom: 2px solid #dee2e6;
            background-color: #f8f9fa;
        }
        .markdown-content pre {
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.25rem;
            overflow: auto;
        }
        .markdown-content code {
            background-color: #f8f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 0.2rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- 侧边栏 -->
            <div class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky">
                    <h3 class="text-center mb-4">我的知识库</h3>
                    
                    <!-- 搜索框 -->
                    <form class="mb-4 px-3" action="{% url 'knowledge_core:search' %}" method="get">
                        <div class="input-group">
                            <input type="text" class="form-control" name="q" placeholder="Search..." value="{{ request.GET.q }}">
                            <button class="btn btn-primary" type="submit">
                                <i class="bi bi-search"></i>
                            </button>
                        </div>
                    </form>
                    
                    <!-- 导航 -->
                    <ul class="nav flex-column">
                        <li class="nav-item">
                            <a class="nav-link active" href="{% url 'knowledge_core:index' %}">
                                <i class="bi bi-house-door me-2"></i> 首页
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-bookmark me-2"></i> 分类
                            </a>
                            <ul class="list-unstyled ms-4">
                                {% for category in categories %}
                                <li>
                                    <a href="{% url 'knowledge_core:category_knowledges' category.slug %}">
                                        <i class="bi bi-folder me-1"></i> {{ category.name }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#">
                                <i class="bi bi-tag me-2"></i> 标签
                            </a>
                            <div class="d-flex flex-wrap ms-4 mt-2">
                                {% for tag in tags %}
                                <a href="{% url 'knowledge_core:tag_knowledges' tag.slug %}" class="badge bg-secondary me-1 mb-1">
                                    {{ tag.name }}
                                </a>
                                {% endfor %}
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            
            <!-- 主内容区域 -->
            <div class="col-md-9 col-lg-10 main-content">
                {% if messages %}
                    {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                    {% endfor %}
                {% endif %}
                
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>