{% extends "base.html" %}

{% block title %}分类管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="bi bi-folder"></i> 分类管理</h1>
        <a href="{% url 'knowledge_core:category_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 新建分类
        </a>
    </div>

    {% if categories %}
        <div class="row">
            {% for category in categories %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="bi bi-folder"></i> 
                            <a href="{% url 'knowledge_core:category_detail' category.slug %}" class="text-decoration-none">
                                {{ category.name }}
                            </a>
                        </h5>
                        
                        {% if category.parent %}
                        <p class="text-muted small mb-2">
                            <i class="bi bi-arrow-up-right"></i> 
                            父分类: <a href="{% url 'knowledge_core:category_detail' category.parent.slug %}">{{ category.parent.name }}</a>
                        </p>
                        {% endif %}
                        
                        {% if category.description %}
                        <p class="card-text text-muted">{{ category.description|truncatewords:15 }}</p>
                        {% endif %}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <span class="badge bg-primary">
                                {{ category.knowledge_count }} 个知识条目
                            </span>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'knowledge_core:category_detail' category.slug %}" 
                                   class="btn btn-outline-primary" title="查看">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'knowledge_core:category_edit' category.slug %}" 
                                   class="btn btn-outline-secondary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                                <a href="{% url 'knowledge_core:category_delete' category.slug %}" 
                                   class="btn btn-outline-danger" title="删除">
                                    <i class="bi bi-trash"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- 分页 -->
        {% if is_paginated %}
        <nav aria-label="分类分页">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1">首页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}">上一页</a>
                    </li>
                {% endif %}

                <li class="page-item active">
                    <span class="page-link">
                        第 {{ page_obj.number }} 页，共 {{ page_obj.paginator.num_pages }} 页
                    </span>
                </li>

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}">下一页</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}">末页</a>
                    </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-folder-x" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3 text-muted">暂无分类</h3>
            <p class="text-muted">开始创建您的第一个分类吧！</p>
            <a href="{% url 'knowledge_core:category_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建分类
            </a>
        </div>
    {% endif %}
</div>
{% endblock %}
