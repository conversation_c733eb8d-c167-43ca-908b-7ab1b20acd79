{% extends "base.html" %}

{% block title %}{{ word.name }} - 单词详情{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 单词头部信息 -->
    <div class="d-flex justify-content-between align-items-start mb-4">
        <div>
            <h1>{{ word.name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'english:word_list' %}">英语单词库</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">{{ word.name }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{% url 'english:word_list' %}" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回列表
            </a>
            {% if user.is_authenticated and user == word.author or user.is_superuser %}
            <a href="{% url 'english:word_edit' word.slug %}" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-pencil"></i> 编辑
            </a>
            <a href="{% url 'english:word_delete' word.slug %}" class="btn btn-sm btn-outline-danger">
                <i class="bi bi-trash"></i> 删除
            </a>
            {% endif %}
        </div>
    </div>

    <div class="row">
        <!-- 主要内容 -->
        <div class="col-lg-8">
            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="card-title">{{ word.name }}</h2>
                    
                    <!-- 音标 -->
                    {% if word.phonetic_us or word.phonetic_uk %}
                    <div class="mb-3">
                        {% if word.phonetic_us %}
                        <span class="badge bg-primary me-2">
                            <i class="bi bi-volume-up"></i> 美式: {{ word.phonetic_us }}
                        </span>
                        {% endif %}
                        {% if word.phonetic_uk %}
                        <span class="badge bg-info">
                            <i class="bi bi-volume-up"></i> 英式: {{ word.phonetic_uk }}
                        </span>
                        {% endif %}
                    </div>
                    {% endif %}
                    
                    <!-- 音频 -->
                    {% if word.audio_us or word.audio_uk %}
                    <div class="mb-3">
                        {% if word.audio_us %}
                        <audio controls class="me-3">
                            <source src="{{ word.audio_us }}" type="audio/mpeg">
                            您的浏览器不支持音频播放。
                        </audio>
                        <small class="text-muted">美式发音</small>
                        {% endif %}
                        {% if word.audio_uk %}
                        <audio controls class="me-3">
                            <source src="{{ word.audio_uk }}" type="audio/mpeg">
                            您的浏览器不支持音频播放。
                        </audio>
                        <small class="text-muted">英式发音</small>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>

            <!-- 词义 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-book"></i> 词义</h5>
                </div>
                <div class="card-body">
                    {% if word.meanings.exists %}
                        {% for meaning in word.meanings.all %}
                        <div class="mb-3 pb-3 {% if not forloop.last %}border-bottom{% endif %}">
                            <div class="d-flex align-items-center mb-2">
                                {% if meaning.part_of_speech %}
                                <span class="badge bg-secondary me-2">{{ meaning.get_part_of_speech_display }}</span>
                                {% endif %}
                                <span class="fw-bold">{{ meaning.translation }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">暂无词义信息</p>
                    {% endif %}
                </div>
            </div>

            <!-- 词形变化 -->
            {% if word.past_tense or word.past_participle or word.plural_form %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-arrow-repeat"></i> 词形变化</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% if word.past_tense %}
                        <div class="col-md-4 mb-2">
                            <strong>过去式:</strong> {{ word.past_tense }}
                        </div>
                        {% endif %}
                        {% if word.past_participle %}
                        <div class="col-md-4 mb-2">
                            <strong>过去分词:</strong> {{ word.past_participle }}
                        </div>
                        {% endif %}
                        {% if word.plural_form %}
                        <div class="col-md-4 mb-2">
                            <strong>复数形式:</strong> {{ word.plural_form }}
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 例句 -->
            {% if word.example_sentences %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-chat-quote"></i> 例句</h5>
                </div>
                <div class="card-body">
                    <div class="example-sentences">
                        {{ word.example_sentences|linebreaks }}
                    </div>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 标签 -->
            {% if word.tags.exists %}
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">标签</h6>
                    <div class="d-flex flex-wrap">
                        {% for tag in word.tags.all %}
                        <span class="badge bg-secondary me-1 mb-1">{{ tag.name }}</span>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 单词信息 -->
            <div class="card mb-3">
                <div class="card-body">
                    <h6 class="card-title">单词信息</h6>
                    <ul class="list-unstyled">
                        <li><strong>创建者:</strong> {{ word.author.username }}</li>
                        <li><strong>创建时间:</strong> {{ word.created_at|date:"Y-m-d H:i" }}</li>
                        <li><strong>更新时间:</strong> {{ word.updated_at|date:"Y-m-d H:i" }}</li>
                        <li><strong>词义数量:</strong> {{ word.meanings.count }}</li>
                    </ul>
                </div>
            </div>

            <!-- 学习提示 -->
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-lightbulb"></i> 学习提示</h6>
                    <ul class="small text-muted mb-0">
                        <li>多听音频，练习正确发音</li>
                        <li>通过例句理解单词用法</li>
                        <li>注意词形变化的规律</li>
                        <li>结合语境记忆单词含义</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
