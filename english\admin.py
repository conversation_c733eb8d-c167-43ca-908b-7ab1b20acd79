from django.contrib import admin
from .models import Word, Meaning, Tag


class MeaningInline(admin.TabularInline):  # 或 admin.StackedInline
    model = Meaning
    extra = 1  # 默认显示 1 个空行


@admin.register(Word)
class WordAdmin(admin.ModelAdmin):
    list_display = ('name', 'past_tense', 'past_participle', 'plural_form')
    search_fields = ('name',)
    inlines = [MeaningInline]


@admin.register(Tag)
class TagAdmin(admin.ModelAdmin):
    list_display = ('name',)


@admin.register(Meaning)
class MeaningAdmin(admin.ModelAdmin):
    list_display = ('word', 'part_of_speech', 'translation')
    search_fields = ('translation',)
