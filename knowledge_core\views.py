from django.shortcuts import render, get_object_or_404, redirect
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib import messages
from django.db.models import Q
from django.urls import reverse, reverse_lazy
from .models import KnowledgeEntry, Category, Tag
from .forms import KnowledgeEntryForm, CategoryForm
from django.db.models import Count

class KnowledgeEntryListView(ListView):
    model = KnowledgeEntry
    template_name = 'knowledge_core/index.html'
    context_object_name = 'knowledges'
    paginate_by = 10
    
    def get_queryset(self):
        return KnowledgeEntry.objects.all().order_by('-updated_at')

class KnowledgeEntryDetailView(DetailView):
    model = KnowledgeEntry
    template_name = 'knowledge_core/knowledge_detail.html'
    context_object_name = 'knowledge'
    
    def get_object(self):
        return get_object_or_404(KnowledgeEntry, slug=self.kwargs['slug'])
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['related_knowledges'] = KnowledgeEntry.objects.filter(
            category=self.object.category
        ).exclude(id=self.object.id)[:5]
        return context

class KnowledgeEntryCreateView(LoginRequiredMixin, CreateView):
    model = KnowledgeEntry
    form_class = KnowledgeEntryForm
    template_name = 'knowledge_core/knowledge_form.html'
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        messages.success(self.request, 'KnowledgeEntry created successfully!')
        return super().form_valid(form)
    
    def get_success_url(self):
        return self.object.get_absolute_url()

class KnowledgeEntryUpdateView(LoginRequiredMixin, UpdateView):
    model = KnowledgeEntry
    form_class = KnowledgeEntryForm
    template_name = 'knowledge_core/knowledge_form.html'
    context_object_name = 'knowledge'
    
    def get_object(self):
        return get_object_or_404(KnowledgeEntry, slug=self.kwargs['slug'])
    
    def form_valid(self, form):
        messages.success(self.request, 'KnowledgeEntry updated successfully!')
        return super().form_valid(form)
    
    def get_success_url(self):
        return self.object.get_absolute_url()

class KnowledgeEntryDeleteView(LoginRequiredMixin, DeleteView):
    model = KnowledgeEntry
    template_name = 'knowledge_core/knowledge_confirm_delete.html'
    
    def get_object(self):
        return get_object_or_404(KnowledgeEntry, slug=self.kwargs['slug'])
    
    def get_success_url(self):
        messages.success(self.request, 'KnowledgeEntry deleted successfully!')
        return reverse('index')

def index(request):
    context = {
        'total_knowledges': KnowledgeEntry.objects.count(),
        'total_categories': Category.objects.count(),
        'total_tags': Tag.objects.count(),
        'last_update': KnowledgeEntry.objects.order_by('-updated_at').first(),
        'latest_knowledges': KnowledgeEntry.objects.all()
                             .order_by('-updated_at')[:6],
        'categories': Category.objects.annotate(num_knowledges=Count('knowledgeentry'))
                             .order_by('-num_knowledges')[:8],
        'popular_tags': Tag.objects.annotate(num_knowledges=Count('knowledgeentry'))
                           .order_by('-num_knowledges')[:20],
    }
    return render(request, 'knowledge_core/index.html', context)

def category_knowledges(request, slug):
    category = get_object_or_404(Category, slug=slug)
    knowledges = KnowledgeEntry.objects.filter(category=category).order_by('-updated_at')
    return render(request, 'knowledge_core/category_knowledges.html', {
        'category': category,
        'knowledges': knowledges
    })

def tag_knowledges(request, slug):
    tag = get_object_or_404(Tag, slug=slug)
    knowledges = KnowledgeEntry.objects.filter(tags=tag).order_by('-updated_at')
    return render(request, 'knowledge_core/tag_knowledges.html', {
        'tag': tag,
        'knowledges': knowledges
    })

def search(request):
    query = request.GET.get('q', '')
    if query:
        knowledges = KnowledgeEntry.objects.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(category__name__icontains=query) |
            Q(tags__name__icontains=query)
        ).distinct().order_by('-updated_at')
    else:
        knowledges = []

    return render(request, 'knowledge_core/search.html', {
        'knowledges': knowledges,
        'query': query
    })


# Category CRUD Views
class CategoryListView(ListView):
    model = Category
    template_name = 'knowledge_core/category_list.html'
    context_object_name = 'categories'
    paginate_by = 20

    def get_queryset(self):
        return Category.objects.annotate(
            knowledge_count=Count('knowledgeentry')
        ).order_by('name')


class CategoryDetailView(DetailView):
    model = Category
    template_name = 'knowledge_core/category_detail.html'
    context_object_name = 'category'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['knowledges'] = KnowledgeEntry.objects.filter(
            category=self.object
        ).order_by('-updated_at')
        context['subcategories'] = Category.objects.filter(
            parent=self.object
        ).annotate(knowledge_count=Count('knowledgeentry'))
        return context


class CategoryCreateView(LoginRequiredMixin, CreateView):
    model = Category
    form_class = CategoryForm
    template_name = 'knowledge_core/category_form.html'

    def form_valid(self, form):
        messages.success(self.request, '分类创建成功！')
        return super().form_valid(form)


class CategoryUpdateView(LoginRequiredMixin, UpdateView):
    model = Category
    form_class = CategoryForm
    template_name = 'knowledge_core/category_form.html'

    def form_valid(self, form):
        messages.success(self.request, '分类更新成功！')
        return super().form_valid(form)


class CategoryDeleteView(LoginRequiredMixin, DeleteView):
    model = Category
    template_name = 'knowledge_core/category_confirm_delete.html'
    success_url = reverse_lazy('knowledge_core:category_list')

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, '分类删除成功！')
        return super().delete(request, *args, **kwargs)