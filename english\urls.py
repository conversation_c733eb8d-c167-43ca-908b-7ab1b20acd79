from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from english.views import WordViewSet, MeaningViewSet, TagViewSet
from . import views

app_name = "english"

urlpatterns = [
    # 首页
    path('', views.WordListView.as_view(), name='word_list'),

    # 单词相关
    path('words/', views.WordListView.as_view(), name='word_list_alt'),
    path('word/new/', views.WordCreateView.as_view(), name='word_create'),
    path('word/<str:slug>/', views.WordDetailView.as_view(), name='word_detail'),
    path('word/<str:slug>/edit/', views.WordUpdateView.as_view(), name='word_edit'),
    path('word/<str:slug>/delete/', views.WordDeleteView.as_view(), name='word_delete'),
]
