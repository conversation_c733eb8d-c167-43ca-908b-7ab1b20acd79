from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from english.views import WordViewSet, MeaningViewSet, TagViewSet
from . import views

app_name = "english"

urlpatterns = [
    # 单词相关
    path('words/', views.WordListView.as_view(), name='word_list'),
    path('word/<slug:slug>/', views.WordDetailView.as_view(), name='word_detail'),
    path('word/create/', views.WordCreateView.as_view(), name='word_create'),
    path('word/<slug:slug>/edit/', views.WordUpdateView.as_view(), name='word_edit'),
    path('word/<slug:slug>/delete/', views.WordDeleteView.as_view(), name='word_delete'),
]
