[ERROR] 2025-07-10 10:21:48,099 django.request | Internal Server Error: /knowledge_core/knowledge/new/
Traceback (most recent call last):
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\views\generic\base.py", line 105, in view
    return self.dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\contrib\auth\mixins.py", line 73, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\views\generic\base.py", line 144, in dispatch
    return handler(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\views\generic\edit.py", line 182, in post
    return super().post(request, *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\views\generic\edit.py", line 151, in post
    return self.form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\views.py", line 43, in form_valid
    return super().form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\views\generic\edit.py", line 134, in form_valid
    return super().form_valid(form)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\views\generic\edit.py", line 63, in form_valid
    return HttpResponseRedirect(self.get_success_url())
                                ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\views.py", line 46, in get_success_url
    return self.object.get_absolute_url()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\models.py", line 37, in get_absolute_url
    return reverse('knowledge_core/knowledge_detail', args=[self.slug])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'knowledge_core/knowledge_detail' not found. 'knowledge_core/knowledge_detail' is not a valid view function or pattern name.
[ERROR] 2025-07-10 10:21:48,317 django.server | "POST /knowledge_core/knowledge/new/ HTTP/1.1" 500 135691
[ERROR] 2025-07-10 10:25:22,796 django.request | Internal Server Error: /knowledge_core/knowledge/xin-jian-zhi-shi-tiao-mu-ce-shi-1-3/
Traceback (most recent call last):
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
               ^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'category_knowledges' with arguments '('',)' not found. 1 pattern(s) tried: ['knowledge_core/category/(?P<slug>[-a-zA-Z0-9_]+)/\\Z']
[ERROR] 2025-07-10 10:25:22,971 django.server | "GET /knowledge_core/knowledge/xin-jian-zhi-shi-tiao-mu-ce-shi-1-3/ HTTP/1.1" 500 177207
[ERROR] 2025-07-10 13:34:24,576 django.request | Internal Server Error: /knowledge_core/search/
Traceback (most recent call last):
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\views.py", line 118, in search
    return render(request, 'knowledge_core/search.html', {
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader.py", line 19, in get_template
    raise TemplateDoesNotExist(template_name, chain=chain)
django.template.exceptions.TemplateDoesNotExist: knowledge_core/search.html
[ERROR] 2025-07-10 13:34:24,716 django.server | "GET /knowledge_core/search/?q=%E6%B5%8B%E8%AF%95 HTTP/1.1" 500 102012
[ERROR] 2025-07-10 13:42:50,424 django.request | Internal Server Error: /knowledge_core/search/
Traceback (most recent call last):
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\views.py", line 118, in search
    return render(request, 'knowledge_core/search.html', {
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 925, in _resolve_lookup
    current = current()
              ^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\models.py", line 37, in get_absolute_url
    return reverse('knowledge_core:knowledge_detail', args=[self.slug])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'knowledge_detail' with arguments '('新建知识条目测试1',)' not found. 1 pattern(s) tried: ['knowledge_core/knowledge/(?P<slug>[-a-zA-Z0-9_]+)/\\Z']
[ERROR] 2025-07-10 13:42:50,783 django.server | "GET /knowledge_core/search/?q=%E6%B5%8B%E8%AF%95 HTTP/1.1" 500 247450
[ERROR] 2025-07-10 13:44:25,994 django.request | Internal Server Error: /knowledge_core/search/
Traceback (most recent call last):
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\views.py", line 118, in search
    return render(request, 'knowledge_core/search.html', {
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader_tags.py", line 65, in render
    result = block.nodelist.render(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 327, in render
    return nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 243, in render
    nodelist.append(node.render_annotated(context))
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1075, in render
    output = self.filter_expression.resolve(context)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 722, in resolve
    obj = self.var.resolve(context)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 854, in resolve
    value = self._resolve_lookup(context)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 925, in _resolve_lookup
    current = current()
              ^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\models.py", line 37, in get_absolute_url
    return reverse('knowledge_core:knowledge_detail', args=[self.slug])
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'knowledge_detail' with arguments '('新建知识条目测试1',)' not found. 1 pattern(s) tried: ['knowledge_core/knowledge/(?P<slug>[-a-zA-Z0-9_]+)/\\Z']
[ERROR] 2025-07-10 13:44:26,249 django.server | "GET /knowledge_core/search/?q=%E6%B5%8B%E8%AF%95 HTTP/1.1" 500 247451
