{% extends "base.html" %}

{% block title %}Markdown编辑器演示{% endblock %}

{% block extra_css %}
    <!-- Markdown预览样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown-light.min.css">
    <style>
        .markdown-editor-container {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            overflow: hidden;
        }
        
        .editor-toolbar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .editor-tabs {
            display: flex;
            gap: 1rem;
        }
        
        .editor-tab {
            padding: 0.25rem 0.75rem;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }
        
        .editor-tab.active {
            background-color: #007bff;
            color: white;
        }
        
        .editor-tab:hover:not(.active) {
            background-color: #e9ecef;
        }
        
        .editor-content {
            display: flex;
            height: 500px;
        }
        
        .editor-pane {
            flex: 1;
            display: flex;
            flex-direction: column;
        }
        
        .editor-pane.hidden {
            display: none;
        }
        
        .editor-textarea {
            flex: 1;
            border: none;
            outline: none;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
        }
        
        .preview-pane {
            flex: 1;
            border-left: 1px solid #dee2e6;
            overflow-y: auto;
            padding: 1rem;
            background-color: #fff;
        }
        
        .preview-pane.hidden {
            display: none;
        }
        
        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 100%;
            margin: 0;
        }
        
        .editor-help {
            font-size: 0.875rem;
            color: #6c757d;
        }
        
        @media (max-width: 768px) {
            .editor-content {
                flex-direction: column;
                height: auto;
            }
            
            .preview-pane {
                border-left: none;
                border-top: 1px solid #dee2e6;
                min-height: 300px;
            }
            
            .editor-textarea {
                min-height: 300px;
            }
        }
    </style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h1><i class="bi bi-markdown"></i> Markdown编辑器演示</h1>
            <p class="text-muted">体验实时Markdown预览功能</p>
            
            <div class="markdown-editor-container">
                <div class="editor-toolbar">
                    <div class="editor-tabs">
                        <button type="button" class="editor-tab active" data-tab="edit">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button type="button" class="editor-tab" data-tab="preview">
                            <i class="bi bi-eye"></i> 预览
                        </button>
                        <button type="button" class="editor-tab" data-tab="split">
                            <i class="bi bi-layout-split"></i> 分屏
                        </button>
                    </div>
                    <div class="editor-help">
                        支持Markdown语法 | Ctrl+P 切换预览 | Ctrl+Shift+P 分屏模式
                    </div>
                </div>
                
                <div class="editor-content">
                    <div class="editor-pane" id="editor-pane">
                        <textarea 
                            class="editor-textarea" 
                            id="markdown-editor"
                            placeholder="在这里输入Markdown内容..."># 欢迎使用Markdown编辑器

这是一个**实时预览**的Markdown编辑器。

## 功能特性

- ✅ 实时预览
- ✅ 分屏模式
- ✅ 语法高亮
- ✅ 键盘快捷键

## 支持的语法

### 标题
```
# 一级标题
## 二级标题
### 三级标题
```

### 文本格式
- **粗体文本**
- *斜体文本*
- ~~删除线~~
- `行内代码`

### 列表
1. 有序列表项1
2. 有序列表项2

- 无序列表项1
- 无序列表项2

### 代码块
```python
def hello_world():
    print("Hello, World!")
```

### 链接和图片
[链接文本](https://example.com)

### 表格
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

### 引用
> 这是一个引用块
> 可以包含多行内容

---

开始编辑以查看实时预览效果！</textarea>
                    </div>
                    <div class="preview-pane hidden" id="preview-pane">
                        <div class="markdown-body" id="preview-content">
                            <p class="text-muted">开始输入以查看预览...</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4">
                <div class="row">
                    <div class="col-md-6">
                        <h5>快捷键</h5>
                        <ul class="list-unstyled">
                            <li><kbd>Ctrl</kbd> + <kbd>P</kbd> - 切换预览模式</li>
                            <li><kbd>Ctrl</kbd> + <kbd>Shift</kbd> + <kbd>P</kbd> - 分屏模式</li>
                            <li><kbd>Tab</kbd> - 插入4个空格缩进</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5>支持的功能</h5>
                        <ul class="list-unstyled">
                            <li>✅ GitHub风格Markdown</li>
                            <li>✅ 代码语法高亮</li>
                            <li>✅ 表格支持</li>
                            <li>✅ 任务列表</li>
                            <li>✅ 自动链接</li>
                            <li>✅ XSS防护</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- DOMPurify for XSS protection -->
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('markdown-editor');
            const previewContent = document.getElementById('preview-content');
            const editorPane = document.getElementById('editor-pane');
            const previewPane = document.getElementById('preview-pane');
            const tabs = document.querySelectorAll('.editor-tab');

            // 配置marked选项
            marked.setOptions({
                breaks: true,
                gfm: true,
                headerIds: false,
                mangle: false
            });

            // 更新预览内容
            function updatePreview() {
                const markdownText = textarea.value;
                if (markdownText.trim() === '') {
                    previewContent.innerHTML = '<p class="text-muted">开始输入以查看预览...</p>';
                } else {
                    try {
                        const html = marked.parse(markdownText);
                        const cleanHtml = DOMPurify.sanitize(html);
                        previewContent.innerHTML = cleanHtml;
                    } catch (error) {
                        previewContent.innerHTML = '<p class="text-danger">预览解析错误</p>';
                    }
                }
            }

            // 切换视图模式
            function switchTab(mode) {
                tabs.forEach(tab => {
                    tab.classList.remove('active');
                    if (tab.dataset.tab === mode) {
                        tab.classList.add('active');
                    }
                });

                switch (mode) {
                    case 'edit':
                        editorPane.classList.remove('hidden');
                        previewPane.classList.add('hidden');
                        textarea.focus();
                        break;
                    case 'preview':
                        editorPane.classList.add('hidden');
                        previewPane.classList.remove('hidden');
                        updatePreview();
                        break;
                    case 'split':
                        editorPane.classList.remove('hidden');
                        previewPane.classList.remove('hidden');
                        updatePreview();
                        break;
                }
            }

            // 绑定事件
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });

            textarea.addEventListener('input', function() {
                if (!previewPane.classList.contains('hidden')) {
                    updatePreview();
                }
            });

            // 键盘快捷键
            textarea.addEventListener('keydown', function(e) {
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    const currentTab = document.querySelector('.editor-tab.active').dataset.tab;
                    if (currentTab === 'edit') {
                        switchTab('preview');
                    } else if (currentTab === 'preview') {
                        switchTab('edit');
                    }
                }
                
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
                    e.preventDefault();
                    switchTab('split');
                }

                if (e.key === 'Tab') {
                    e.preventDefault();
                    const start = this.selectionStart;
                    const end = this.selectionEnd;
                    this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                    this.selectionStart = this.selectionEnd = start + 4;
                }
            });

            // 初始化预览
            updatePreview();
        });
    </script>
{% endblock %}
