{% extends 'base.html' %} {# 继承你的基础模板，例如包含导航栏、页脚等 #}
{% load static %} {# 如果需要加载静态文件，比如 CSS 或 JS #}

{% block title %}搜索结果{% if query %} - "{{ query }}"{% endif %}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-lg-8 offset-lg-2">
            <h2 class="mb-4 text-center">搜索知识库</h2>

            {# 搜索表单 #}
            <form action="{% url 'knowledge_core:search' %}" method="get" class="mb-5">
                <div class="input-group input-group-lg">
                    <input type="text"
                           class="form-control"
                           placeholder="输入关键词进行搜索..."
                           aria-label="搜索关键词"
                           name="q" {# 确保你的搜索视图从 request.GET.get('q') 获取关键词 #}
                           value="{{ query|default:'' }}"> {# 默认显示上次搜索的关键词 #}
                    <button class="btn btn-primary" type="submit">搜索</button>
                </div>
            </form>

            {# 搜索结果部分 #}
            <hr>
            {% if query %}
                <h3 class="mb-4">关于 "{{ query }}" 的搜索结果</h3>

                {% if knowledges %}
                    <p class="text-muted">找到 {{ knowledges.count }} 条结果。</p>

                    <div class="list-group">
                        {% for entry in knowledges %}
                            <a href="{{ entry.get_absolute_url }}" class="list-group-item list-group-item-action mb-3 shadow-sm rounded">
                                <div class="d-flex w-100 justify-content-between">
                                    <h5 class="mb-1 text-primary">{{ entry.title }}</h5>
                                    <small class="text-muted">{{ entry.created_at|date:"Y年m月d日" }}</small>
                                </div>
                                <p class="mb-1 text-dark">
                                    {# 显示部分内容或摘要，避免直接显示整个 content #}
                                    {% if entry.content|length > 200 %}
                                        {{ entry.content|striptags|truncatechars:200 }}...
                                    {% else %}
                                        {{ entry.content|striptags }}
                                    {% endif %}
                                </p>
                                <small class="text-muted">作者: {{ entry.created_by.username }}</small>
                                {% if entry.category %}
                                    <small class="text-muted"> | 分类: {{ entry.category.name }}</small>
                                {% endif %}
                                {% if entry.tags.all %}
                                    <br><small class="text-muted">标签:
                                    {% for tag in entry.tags.all %}
                                        {{ tag.name }}{% if not forloop.last %}, {% endif %}
                                    {% endfor %}
                                    </small>
                                {% endif %}
                            </a>
                        {% endfor %}
                    </div>

                    {# 如果需要分页，在这里添加分页链接 #}
                    {# {% if is_paginated %} #}
                    {#     <nav aria-label="Page navigation"> #}
                    {#         <ul class="pagination justify-content-center mt-4"> #}
                    {#             {% if page_obj.has_previous %} #}
                    {#                 <li class="page-item"><a class="page-link" href="?q={{ query }}&page={{ page_obj.previous_page_number }}">上一页</a></li> #}
                    {#             {% endif %} #}
                    {#             <li class="page-item active"><a class="page-link" href="#">{{ page_obj.number }}</a></li> #}
                    {#             {% if page_obj.has_next %} #}
                    {#                 <li class="page-item"><a class="page-link" href="?q={{ query }}&page={{ page_obj.next_page_number }}">下一页</a></li> #}
                    {#             {% endif %} #}
                    {#         </ul> #}
                    {#     </nav> #}
                    {# {% endif %} #}

                {% else %}
                    <div class="alert alert-info text-center" role="alert">
                        没有找到与 "{{ query }}" 相关的知识条目。
                    </div>
                {% endif %}

            {% else %}
                <div class="alert alert-info text-center" role="alert">
                    请输入关键词开始搜索。
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}