{% extends "base.html" %}

{% block title %}
    {% if form.instance.pk %}
        编辑标签 - {{ form.instance.name }}
    {% else %}
        创建新标签
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-success text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-tag"></i>
                        {% if form.instance.pk %}
                            编辑标签
                        {% else %}
                            创建新标签
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- 标签名称 -->
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.name.help_text %}
                                <div class="form-text">{{ form.name.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-between">
                            <a href="{% if form.instance.pk %}{% url 'knowledge_core:tag_detail' form.instance.slug %}{% else %}{% url 'knowledge_core:tag_list' %}{% endif %}" 
                               class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-success">
                                <i class="bi bi-check-circle"></i>
                                {% if form.instance.pk %}
                                    更新标签
                                {% else %}
                                    创建标签
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 帮助信息 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-info-circle"></i> 使用提示</h6>
                    <ul class="mb-0 small text-muted">
                        <li>标签名称应该简洁明了，便于识别</li>
                        <li>建议使用2-10个字符的标签名称</li>
                        <li>标签名称必须唯一，不能与现有标签重复</li>
                        <li>创建后会自动生成URL友好的slug</li>
                        <li>标签可以用于多个知识条目</li>
                    </ul>
                </div>
            </div>

            <!-- 标签示例 -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-lightbulb"></i> 标签示例</h6>
                    <div class="d-flex flex-wrap">
                        <span class="badge bg-primary me-1 mb-1">Python</span>
                        <span class="badge bg-secondary me-1 mb-1">编程</span>
                        <span class="badge bg-success me-1 mb-1">教程</span>
                        <span class="badge bg-info me-1 mb-1">数据库</span>
                        <span class="badge bg-warning me-1 mb-1">前端</span>
                        <span class="badge bg-danger me-1 mb-1">后端</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
