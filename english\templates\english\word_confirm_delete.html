{% extends "base.html" %}

{% block title %}删除单词 - {{ word.name }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> 确认删除单词
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <strong>警告：</strong> 此操作不可撤销！
                    </div>

                    <p>您确定要删除单词 <strong>"{{ word.name }}"</strong> 吗？</p>

                    <!-- 单词信息 -->
                    <div class="mb-3">
                        <h6>单词详情：</h6>
                        <ul class="list-unstyled">
                            <li><strong>单词:</strong> {{ word.name }}</li>
                            {% if word.phonetic_us %}
                            <li><strong>美式音标:</strong> {{ word.phonetic_us }}</li>
                            {% endif %}
                            {% if word.phonetic_uk %}
                            <li><strong>英式音标:</strong> {{ word.phonetic_uk }}</li>
                            {% endif %}
                            {% if word.meanings.exists %}
                            <li><strong>词义数量:</strong> {{ word.meanings.count }} 个</li>
                            {% endif %}
                            {% if word.tags.exists %}
                            <li><strong>标签:</strong> 
                                {% for tag in word.tags.all %}
                                    <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                                {% endfor %}
                            </li>
                            {% endif %}
                            <li><strong>创建时间:</strong> {{ word.created_at|date:"Y-m-d H:i" }}</li>
                            <li><strong>创建者:</strong> {{ word.author.username }}</li>
                        </ul>
                    </div>

                    <!-- 例句预览 -->
                    {% if word.example_sentences %}
                    <div class="mb-3">
                        <h6>例句预览：</h6>
                        <div class="border rounded p-3 bg-light">
                            <p class="text-muted mb-0">{{ word.example_sentences|truncatewords:20|striptags }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'english:word_detail' word.slug %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> 确认删除
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 替代方案 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-lightbulb"></i> 替代方案</h6>
                    <p class="card-text small text-muted">
                        如果您只是想修改单词信息，可以考虑：
                    </p>
                    <ul class="small text-muted mb-0">
                        <li>编辑单词的音标或发音</li>
                        <li>修改或添加词义</li>
                        <li>更新例句内容</li>
                        <li>调整标签分类</li>
                    </ul>
                    <div class="mt-2">
                        <a href="{% url 'english:word_edit' word.slug %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil"></i> 编辑单词
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
