from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from slugify import slugify


class KnowledgeEntry(models.Model):
    title = models.CharField(max_length=200)
    slug = models.SlugField(max_length=200, unique=True, blank=True)
    content = models.TextField()  # Markdown 或 HTML
    # type = models.CharField(max_length=50, choices=[("note", "笔记"), ("word", "单词"), ("code", "代码")])
    tags = models.ManyToManyField("Tag", blank=True)
    category = models.ForeignKey("Category", on_delete=models.SET_NULL, null=True, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated_at']
    
    def __str__(self):
        return self.title
    
    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.title)
            slug = base_slug
            num = 1
            while KnowledgeEntry.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            self.slug = slug
        super().save(*args, **kwargs)
    
    def get_absolute_url(self):
        return reverse('knowledge_core:knowledge_detail', args=[self.slug])
    
    def get_edit_url(self):
        return reverse('knowledge_core:knowledge_edit', args=[self.slug])
    
    def get_delete_url(self):
        return reverse('knowledge_core:knowledge_delete', args=[self.slug])

class Category(models.Model):
    name = models.CharField(max_length=100, unique=True)
    slug = models.SlugField(max_length=100, unique=True, blank=True)
    parent = models.ForeignKey("self", null=True, blank=True, on_delete=models.CASCADE)
    description = models.TextField(blank=True)

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.name)  # 修复：应该使用 self.name 而不是 self.title
            slug = base_slug
            num = 1
            while Category.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('knowledge_core:category_knowledges', args=[self.slug])  # 修复：使用正确的URL名称

    def get_edit_url(self):
        return reverse('knowledge_core:category_edit', args=[self.slug])

    def get_delete_url(self):
        return reverse('knowledge_core:category_delete', args=[self.slug])


class Tag(models.Model):
    name = models.CharField(max_length=30)
    slug = models.SlugField(max_length=50, unique=True, blank=True)

    class Meta:
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.name)  # 修复：应该使用 self.name 而不是 self.title
            slug = base_slug
            num = 1
            while Tag.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('knowledge_core:tag_knowledges', args=[self.slug])
