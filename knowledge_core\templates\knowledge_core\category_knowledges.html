{% extends "base.html" %}

{% block title %}{{ category.name }} - 知识条目{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 分类头部信息 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1><i class="bi bi-folder"></i> {{ category.name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'knowledge_core:category_list' %}">所有分类</a>
                    </li>
                    {% if category.parent %}
                    <li class="breadcrumb-item">
                        <a href="{% url 'knowledge_core:category_detail' category.parent.slug %}">{{ category.parent.name }}</a>
                    </li>
                    {% endif %}
                    <li class="breadcrumb-item active" aria-current="page">{{ category.name }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{% url 'knowledge_core:category_detail' category.slug %}" class="btn btn-outline-info">
                <i class="bi bi-info-circle"></i> 分类详情
            </a>
            <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 新建知识条目
            </a>
        </div>
    </div>

    <!-- 分类描述 -->
    {% if category.description %}
    <div class="alert alert-info" role="alert">
        <i class="bi bi-info-circle"></i>
        {{ category.description }}
    </div>
    {% endif %}

    <!-- 知识条目列表 -->
    {% if knowledges %}
        <div class="row">
            {% for knowledge in knowledges %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" class="text-decoration-none">
                                {{ knowledge.title }}
                            </a>
                        </h5>
                        
                        {% if knowledge.content %}
                        <p class="card-text text-muted">
                            {{ knowledge.content|truncatewords:20|striptags }}
                        </p>
                        {% endif %}
                        
                        <div class="mb-3">
                            {% for tag in knowledge.tags.all %}
                            <a href="{% url 'knowledge_core:tag_knowledges' tag.slug %}" class="badge bg-secondary text-decoration-none me-1">
                                {{ tag.name }}
                            </a>
                            {% endfor %}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                {{ knowledge.updated_at|date:"Y-m-d H:i" }}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" 
                                   class="btn btn-outline-primary" title="查看">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'knowledge_core:knowledge_edit' knowledge.slug %}" 
                                   class="btn btn-outline-secondary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-journal-x" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3 text-muted">该分类下暂无知识条目</h3>
            <p class="text-muted">开始创建您的第一个知识条目吧！</p>
            <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建知识条目
            </a>
        </div>
    {% endif %}

    <!-- 返回按钮 -->
    <div class="mt-4">
        <a href="{% url 'knowledge_core:category_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> 返回分类列表
        </a>
    </div>
</div>
{% endblock %}
