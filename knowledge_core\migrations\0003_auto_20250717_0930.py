# Generated by Django 5.2.4 on 2025-07-17 01:30

from django.db import migrations
from slugify import slugify


def fix_slugs(apps, schema_editor):
    """修复现有记录的slug，使用支持中文的slugify"""
    KnowledgeEntry = apps.get_model('knowledge_core', 'KnowledgeEntry')
    Category = apps.get_model('knowledge_core', 'Category')
    Tag = apps.get_model('knowledge_core', 'Tag')

    # 修复KnowledgeEntry的slug
    for entry in KnowledgeEntry.objects.all():
        if entry.title:
            base_slug = slugify(entry.title)
            slug = base_slug
            num = 1
            while KnowledgeEntry.objects.filter(slug=slug).exclude(pk=entry.pk).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            entry.slug = slug
            entry.save()

    # 修复Category的slug
    for category in Category.objects.all():
        if category.name:
            base_slug = slugify(category.name)
            slug = base_slug
            num = 1
            while Category.objects.filter(slug=slug).exclude(pk=category.pk).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            category.slug = slug
            category.save()

    # 修复Tag的slug
    for tag in Tag.objects.all():
        if tag.name:
            base_slug = slugify(tag.name)
            slug = base_slug
            num = 1
            while Tag.objects.filter(slug=slug).exclude(pk=tag.pk).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            tag.slug = slug
            tag.save()


def reverse_fix_slugs(apps, schema_editor):
    """反向操作（如果需要回滚）"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('knowledge_core', '0002_remove_knowledgeentry_type'),
    ]

    operations = [
        migrations.RunPython(fix_slugs, reverse_fix_slugs),
    ]
