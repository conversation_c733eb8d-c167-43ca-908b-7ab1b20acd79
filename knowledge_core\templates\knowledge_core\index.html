{% extends "base.html" %}
{% load static %}
{% load markdown_extras %}

{% block title %}个人知识库首页{% endblock %}

{% block extra_css %}
<style>
    /* 自定义样式 */
    .hero-section {
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        color: white;
        padding: 4rem 0;
        margin-bottom: 3rem;
        border-radius: 0 0 30px 30px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .feature-card {
        transition: all 0.3s ease;
        height: 100%;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
    }
    
    .feature-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    }
    
    .category-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        font-size: 0.8rem;
        padding: 5px 12px;
        border-radius: 20px;
    }
    
    .knowledge-card {
        border: none;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        transition: all 0.3s ease;
    }
    
    .knowledge-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    }
    
    .knowledge-card img {
        height: 180px;
        object-fit: cover;
        width: 100%;
    }
    
    .tag-cloud {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }
    
    .tag {
        display: inline-block;
        padding: 5px 15px;
        margin: 0 5px 10px 0;
        border-radius: 20px;
        background: #e9ecef;
        color: #495057;
        text-decoration: none;
        transition: all 0.2s ease;
        font-size: 0.9rem;
    }
    
    .tag:hover {
        background: #0d6efd;
        color: white;
        transform: scale(1.05);
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
        text-align: center;
    }
    
    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #0d6efd;
        margin-bottom: 0.5rem;
    }
    
    .stat-label {
        font-size: 0.9rem;
        color: #6c757d;
        text-transform: uppercase;
        letter-spacing: 1px;
    }
    
    .search-section {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }
    
    .btn-knowledge {
        background: #6a11cb;
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        border: none;
        color: white;
        padding: 10px 25px;
        border-radius: 30px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-knowledge:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(106, 17, 203, 0.3);
    }
    
    .section-title {
        position: relative;
        padding-bottom: 15px;
        margin-bottom: 1.5rem;
        font-weight: 700;
    }
    
    .section-title:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 60px;
        height: 4px;
        background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
        border-radius: 2px;
    }
    
    .feature-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #0d6efd;
    }
    
    .knowledge-stats {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
        margin-bottom: 2rem;
    }
    
    @media (max-width: 768px) {
        .hero-section {
            padding: 2rem 0;
        }
        
        .hero-title {
            font-size: 1.8rem;
        }
        
        .knowledge-stats {
            grid-template-columns: 1fr 1fr;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <!-- 知识统计 -->
    <!-- <div class="knowledge-stats">
        <div class="stats-card">
            <div class="stat-number">{{ total_knowledges }}</div>
            <div class="stat-label">知识条目</div>
        </div>
        <div class="stats-card">
            <div class="stat-number">{{ total_categories }}</div>
            <div class="stat-label">分类领域</div>
        </div>
        <div class="stats-card">
            <div class="stat-number">{{ total_tags }}</div>
            <div class="stat-label">知识标签</div>
        </div>
        <div class="stats-card">
            <div class="stat-number">{{ last_update|date:"Y-m-d" }}</div>
            <div class="stat-label">最后更新</div>
        </div>
    </div> -->
    <!-- 核心功能区域 -->
    <!-- <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="feature-card card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon">
                        <i class="bi bi-journal-text"></i>
                    </div>
                    <h5 class="card-title">知识管理</h5>
                    <p class="card-text">使用Markdown格式创建和编辑文章，支持代码块、表格、数学公式等高级格式。</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="feature-card card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon">
                        <i class="bi bi-diagram-3"></i>
                    </div>
                    <h5 class="card-title">知识连接</h5>
                    <p class="card-text">通过分类和标签系统组织知识，发现概念之间的联系，构建个人知识图谱。</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="feature-card card h-100">
                <div class="card-body text-center p-4">
                    <div class="feature-icon">
                        <i class="bi bi-search-heart"></i>
                    </div>
                    <h5 class="card-title">智能检索</h5>
                    <p class="card-text">全文搜索功能，快速定位所需知识，支持按分类、标签和时间筛选内容。</p>
                </div>
            </div>
        </div>
    </div> -->

    <!-- 最新知识 -->
    <div class="row">
        <div class="col-lg-8">
            <h3 class="section-title">最新知识</h3>
            
            {% if latest_knowledges %}
                <div class="row">
                    {% for knowledge in latest_knowledges %}
                    <div class="col-md-6 mb-4">
                        <div class="knowledge-card card h-100">
                            {% if knowledge.featured_image %}
                            <img src="{{ knowledge.featured_image.url }}" class="card-img-top" alt="{{ knowledge.title }}">
                            {% else %}
                            <div class="card-img-top bg-secondary" style="height: 180px; display: flex; align-items: center; justify-content: center;">
                                <i class="bi bi-journal-text" style="font-size: 3rem; color: #6c757d;"></i>
                            </div>
                            {% endif %}
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span class="badge bg-primary">{{ knowledge.category.name }}</span>
                                    <small class="text-muted">{{ knowledge.updated_at|date:"Y-m-d" }}</small>
                                </div>
                                <h5 class="card-title">{{ knowledge.title }}</h5>
                                <p class="card-text text-muted">{{ knowledge.content|striptags|truncatechars:100 }}</p>
                            </div>
                            <div class="card-footer bg-transparent">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        {% for tag in knowledge.tags.all|slice:":2" %}
                                        <span class="badge bg-light text-dark me-1">{{ tag.name }}</span>
                                        {% endfor %}
                                        {% if knowledge.tags.count > 2 %}
                                        <span class="badge bg-light text-dark">+{{ knowledge.tags.count|add:"-2" }}</span>
                                        {% endif %}
                                    </div>
                                    <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" class="btn btn-sm btn-outline-primary">
                                        阅读更多
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="text-center mt-4">
                    <a href="{% url 'knowledge_core:index' %}" class="btn btn-primary">
                        <i class="bi bi-journals me-1"></i> 浏览所有知识
                    </a>
                </div>
            {% else %}
                <div class="alert alert-info text-center py-5">
                    <i class="bi bi-info-circle me-2"></i> 知识库为空，创建你的第一篇知识文章吧！
                </div>
            {% endif %}
        </div>
        
        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <!-- 知识分类 -->
            <div class="card mb-4">
                <div class="card-body">
                    <h3 class="section-title" id="categories">知识分类</h3>
                    <div class="list-group">
                        {% for category in categories %}
                        <a href="{% url 'knowledge_core:category_knowledges' category.slug %}" 
                           class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            <div>
                                <i class="bi bi-folder me-2"></i> {{ category.name }}
                            </div>
                            <span class="badge bg-primary rounded-pill">{{ category.knowledge_set.count }}</span>
                        </a>
                        {% empty %}
                        <div class="text-center py-3 text-muted">
                            <i class="bi bi-exclamation-circle me-2"></i> 暂无分类
                        </div>
                        {% endfor %}
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'knowledge_core:category_create' %}" class="btn btn-outline-primary w-100">
                            <i class="bi bi-plus-circle me-1"></i> 添加新分类
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 热门标签 -->
            <div class="card mb-4">
                <div class="card-body">
                    <h3 class="section-title">热门标签</h3>
                    <div class="tag-cloud">
                        {% for tag in popular_tags %}
                        <a href="{% url 'knowledge_core:tag_knowledges' tag.slug %}" class="tag">
                            <i class="bi bi-tag me-1"></i> {{ tag.name }}
                        </a>
                        {% empty %}
                        <div class="text-center py-3 text-muted">
                            <i class="bi bi-exclamation-circle me-2"></i> 暂无标签
                        </div>
                        {% endfor %}
                    </div>
                    <div class="mt-3">
                        <a href="{% url 'knowledge_core:tag_create' %}" class="btn btn-outline-success w-100">
                            <i class="bi bi-plus-circle me-1"></i> 添加新标签
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- 快捷操作 -->
            <div class="card">
                <div class="card-body">
                    <h3 class="section-title">快捷操作</h3>
                    <div class="d-grid gap-2">
                        <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary">
                            <i class="bi bi-file-earmark-plus me-1"></i> 创建新文章
                        </a>
                        <a href="#" class="btn btn-outline-primary">
                            <i class="bi bi-upload me-1"></i> 导入知识
                        </a>
                        <a href="#" class="btn btn-outline-primary">
                            <i class="bi bi-download me-1"></i> 导出知识库
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 简单的动画效果
    document.addEventListener('DOMContentLoaded', function() {
        // 滚动动画
        const observerOptions = {
            root: null,
            rootMargin: '0px',
            threshold: 0.1
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate__animated', 'animate__fadeInUp');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // 搜索框焦点效果
        const searchInput = document.querySelector('input[name="q"]');
        if (searchInput) {
            searchInput.addEventListener('focus', function() {
                this.parentElement.classList.add('focus-shadow');
            });
            
            searchInput.addEventListener('blur', function() {
                this.parentElement.classList.remove('focus-shadow');
            });
        }
    });
</script>
{% endblock %}