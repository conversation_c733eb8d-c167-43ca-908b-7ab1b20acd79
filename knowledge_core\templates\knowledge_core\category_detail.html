{% extends "base.html" %}

{% block title %}{{ category.name }} - 分类详情{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 分类头部信息 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1><i class="bi bi-folder"></i> {{ category.name }}</h1>
            {% if category.parent %}
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'knowledge_core:category_list' %}">所有分类</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'knowledge_core:category_detail' category.parent.slug %}">{{ category.parent.name }}</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">{{ category.name }}</li>
                </ol>
            </nav>
            {% endif %}
        </div>
        <div class="btn-group">
            <a href="{% url 'knowledge_core:category_edit' category.slug %}" class="btn btn-outline-primary">
                <i class="bi bi-pencil"></i> 编辑
            </a>
            <a href="{% url 'knowledge_core:category_delete' category.slug %}" class="btn btn-outline-danger">
                <i class="bi bi-trash"></i> 删除
            </a>
        </div>
    </div>

    <!-- 分类描述 -->
    {% if category.description %}
    <div class="card mb-4">
        <div class="card-body">
            <h5 class="card-title">分类描述</h5>
            <p class="card-text">{{ category.description|linebreaks }}</p>
        </div>
    </div>
    {% endif %}

    <div class="row">
        <!-- 主要内容区域 -->
        <div class="col-lg-8">
            <!-- 子分类 -->
            {% if subcategories %}
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-folder-plus"></i> 子分类</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for subcategory in subcategories %}
                        <div class="col-md-6 mb-3">
                            <div class="d-flex justify-content-between align-items-center p-3 border rounded">
                                <div>
                                    <h6 class="mb-1">
                                        <a href="{% url 'knowledge_core:category_detail' subcategory.slug %}" class="text-decoration-none">
                                            <i class="bi bi-folder"></i> {{ subcategory.name }}
                                        </a>
                                    </h6>
                                    {% if subcategory.description %}
                                    <small class="text-muted">{{ subcategory.description|truncatewords:10 }}</small>
                                    {% endif %}
                                </div>
                                <span class="badge bg-secondary">{{ subcategory.knowledge_count }}</span>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- 知识条目列表 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-journal-text"></i> 知识条目</h5>
                    <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-sm btn-primary">
                        <i class="bi bi-plus"></i> 新建知识条目
                    </a>
                </div>
                <div class="card-body">
                    {% if knowledges %}
                        {% for knowledge in knowledges %}
                        <div class="border-bottom pb-3 mb-3">
                            <h6>
                                <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" class="text-decoration-none">
                                    {{ knowledge.title }}
                                </a>
                            </h6>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    {% for tag in knowledge.tags.all %}
                                    <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                                    {% endfor %}
                                </div>
                                <small class="text-muted">{{ knowledge.updated_at|date:"Y-m-d H:i" }}</small>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="bi bi-journal-x" style="font-size: 3rem; color: #6c757d;"></i>
                            <h6 class="mt-2 text-muted">该分类下暂无知识条目</h6>
                            <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary mt-2">
                                <i class="bi bi-plus"></i> 创建第一个知识条目
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-body">
                    <h6 class="card-title">分类统计</h6>
                    <ul class="list-unstyled">
                        <li class="d-flex justify-content-between">
                            <span>知识条目数量:</span>
                            <strong>{{ knowledges|length }}</strong>
                        </li>
                        <li class="d-flex justify-content-between">
                            <span>子分类数量:</span>
                            <strong>{{ subcategories|length }}</strong>
                        </li>
                        {% if category.parent %}
                        <li class="d-flex justify-content-between">
                            <span>父分类:</span>
                            <a href="{% url 'knowledge_core:category_detail' category.parent.slug %}">{{ category.parent.name }}</a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
