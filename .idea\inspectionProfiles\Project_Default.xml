<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="LanguageDetectionInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyInterpreterInspection" enabled="false" level="WARNING" enabled_by_default="false" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="35">
            <item index="0" class="java.lang.String" itemvalue="mock" />
            <item index="1" class="java.lang.String" itemvalue="gevent" />
            <item index="2" class="java.lang.String" itemvalue="decorator" />
            <item index="3" class="java.lang.String" itemvalue="pydot" />
            <item index="4" class="java.lang.String" itemvalue="qrcode" />
            <item index="5" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="6" class="java.lang.String" itemvalue="psycopg2" />
            <item index="7" class="java.lang.String" itemvalue="html2text" />
            <item index="8" class="java.lang.String" itemvalue="mako" />
            <item index="9" class="java.lang.String" itemvalue="requests" />
            <item index="10" class="java.lang.String" itemvalue="Jinja2" />
            <item index="11" class="java.lang.String" itemvalue="feedparser" />
            <item index="12" class="java.lang.String" itemvalue="freezegun" />
            <item index="13" class="java.lang.String" itemvalue="docutils" />
            <item index="14" class="java.lang.String" itemvalue="lxml" />
            <item index="15" class="java.lang.String" itemvalue="libsass" />
            <item index="16" class="java.lang.String" itemvalue="pyparsing" />
            <item index="17" class="java.lang.String" itemvalue="passlib" />
            <item index="18" class="java.lang.String" itemvalue="python-stdnum" />
            <item index="19" class="java.lang.String" itemvalue="werkzeug" />
            <item index="20" class="java.lang.String" itemvalue="ofxparse" />
            <item index="21" class="java.lang.String" itemvalue="reportlab" />
            <item index="22" class="java.lang.String" itemvalue="xlwt" />
            <item index="23" class="java.lang.String" itemvalue="pyserial" />
            <item index="24" class="java.lang.String" itemvalue="vobject" />
            <item index="25" class="java.lang.String" itemvalue="xlsxwriter" />
            <item index="26" class="java.lang.String" itemvalue="zeep" />
            <item index="27" class="java.lang.String" itemvalue="psutil" />
            <item index="28" class="java.lang.String" itemvalue="pypdf2" />
            <item index="29" class="java.lang.String" itemvalue="babel" />
            <item index="30" class="java.lang.String" itemvalue="pillow" />
            <item index="31" class="java.lang.String" itemvalue="polib" />
            <item index="32" class="java.lang.String" itemvalue="pyusb" />
            <item index="33" class="java.lang.String" itemvalue="pytz" />
            <item index="34" class="java.lang.String" itemvalue="idna" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>