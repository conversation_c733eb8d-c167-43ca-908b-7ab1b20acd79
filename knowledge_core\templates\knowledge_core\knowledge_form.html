{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if form.instance.pk %}
        编辑知识条目 - {{ form.instance.title }}
    {% else %}
        创建新知识条目
    {% endif %}
{% endblock %}

{% block extra_css %}
    <!-- Markdown预览样式 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown-light.min.css">
    <style>
        .markdown-editor-container {
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            overflow: hidden;
        }

        .editor-toolbar {
            background-color: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            padding: 0.5rem 1rem;
            display: flex;
            justify-content: between;
            align-items: center;
        }

        .editor-tabs {
            display: flex;
            gap: 1rem;
        }

        .editor-tab {
            padding: 0.25rem 0.75rem;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 0.25rem;
            transition: background-color 0.2s;
        }

        .editor-tab.active {
            background-color: #007bff;
            color: white;
        }

        .editor-tab:hover:not(.active) {
            background-color: #e9ecef;
        }

        .editor-content {
            display: flex;
            height: 500px;
        }

        .editor-pane {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .editor-pane.hidden {
            display: none;
        }

        .editor-textarea {
            flex: 1;
            border: none;
            outline: none;
            padding: 1rem;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
        }

        .preview-pane {
            flex: 1;
            border-left: 1px solid #dee2e6;
            overflow-y: auto;
            padding: 1rem;
            background-color: #fff;
        }

        .preview-pane.hidden {
            display: none;
        }

        .markdown-body {
            box-sizing: border-box;
            min-width: 200px;
            max-width: 100%;
            margin: 0;
        }

        .editor-help {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .fullscreen-toggle {
            margin-left: auto;
        }

        @media (max-width: 768px) {
            .editor-content {
                flex-direction: column;
                height: auto;
            }

            .preview-pane {
                border-left: none;
                border-top: 1px solid #dee2e6;
                min-height: 300px;
            }

            .editor-textarea {
                min-height: 300px;
            }
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-7">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            {% if form.instance.pk %}
                                编辑知识条目
                            {% else %}
                                创建新知识条目
                            {% endif %}
                        </h4>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                                <div class="alert alert-danger" role="alert">
                                    {% for error in form.non_field_errors %}
                                        <p class="mb-0">{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            {# 渲染非content字段 #}
                            {% for field in form %}
                                {% if field.name != 'content' and field.name != 'slug' and field.name != 'created_by' and field.name != 'created_at' and field.name != 'updated_at' %}
                                    <div class="mb-3">
                                        <label for="{{ field.id_for_label }}" class="form-label">
                                            {{ field.label }}
                                            {% if field.field.required %}
                                                <span class="text-danger">*</span>
                                            {% endif %}
                                        </label>

                                        {{ field }}

                                        {% if field.help_text %}
                                            <div class="form-text text-muted">{{ field.help_text }}</div>
                                        {% endif %}

                                        {% if field.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in field.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %}
                            {% endfor %}

                            {# 特殊处理content字段 - Markdown编辑器 #}
                            <div class="mb-3">
                                <label for="{{ form.content.id_for_label }}" class="form-label">
                                    {{ form.content.label }}
                                    {% if form.content.field.required %}
                                        <span class="text-danger">*</span>
                                    {% endif %}
                                </label>

                                <div class="markdown-editor-container">
                                    <div class="editor-toolbar">
                                        <div class="editor-tabs">
                                            <button type="button" class="editor-tab active" data-tab="edit">
                                                <i class="bi bi-pencil"></i> 编辑
                                            </button>
                                            <button type="button" class="editor-tab" data-tab="preview">
                                                <i class="bi bi-eye"></i> 预览
                                            </button>
                                            <button type="button" class="editor-tab" data-tab="split">
                                                <i class="bi bi-layout-split"></i> 分屏
                                            </button>
                                        </div>
                                        <div class="editor-help">
                                            支持Markdown语法
                                        </div>
                                    </div>

                                    <div class="editor-content">
                                        <div class="editor-pane" id="editor-pane">
                                            <textarea
                                                class="editor-textarea"
                                                id="{{ form.content.id_for_label }}"
                                                name="{{ form.content.name }}"
                                                placeholder="在这里输入Markdown内容..."
                                                {% if form.content.field.required %}required{% endif %}>{{ form.content.value|default:'' }}</textarea>
                                        </div>
                                        <div class="preview-pane hidden" id="preview-pane">
                                            <div class="markdown-body" id="preview-content">
                                                <p class="text-muted">开始输入以查看预览...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {% if form.content.help_text %}
                                    <div class="form-text text-muted">{{ form.content.help_text }}</div>
                                {% endif %}

                                {% if form.content.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.content.errors %}
                                            {{ error }}
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>

                            <hr>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    {% if form.instance.pk %}
                                        更新知识
                                    {% else %}
                                        创建知识
                                    {% endif %}
                                </button>
                                {% if form.instance.pk %}
                                    <a href="{{ form.instance.get_absolute_url }}" class="btn btn-outline-secondary">取消</a>
                                {% else %}
                                    <a href="{% url 'knowledge_core:index' %}" class="btn btn-outline-secondary">取消</a>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <!-- Marked.js for Markdown parsing -->
    <script src="https://cdn.jsdelivr.net/npm/marked@9.1.6/marked.min.js"></script>
    <!-- DOMPurify for XSS protection -->
    <script src="https://cdn.jsdelivr.net/npm/dompurify@3.0.5/dist/purify.min.js"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 原有的表单样式处理
            document.querySelectorAll('.invalid-feedback').forEach(function(errorDiv) {
                const input = errorDiv.previousElementSibling;
                if (input && (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA' || input.tagName === 'SELECT')) {
                    input.classList.add('is-invalid');
                }
            });

            // 确保如果小部件没有自动添加 Bootstrap 类，则手动添加
            document.querySelectorAll('select').forEach(function(selectElement) {
                selectElement.classList.add('form-select');
            });
            document.querySelectorAll('input:not([type="checkbox"]):not([type="radio"]):not([type="hidden"]), textarea:not(.editor-textarea)').forEach(function(inputElement) {
                inputElement.classList.add('form-control');
            });
            document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(function(inputElement) {
                inputElement.classList.add('form-check-input');
            });

            // Markdown编辑器功能
            const textarea = document.getElementById('{{ form.content.id_for_label }}');
            const previewContent = document.getElementById('preview-content');
            const editorPane = document.getElementById('editor-pane');
            const previewPane = document.getElementById('preview-pane');
            const tabs = document.querySelectorAll('.editor-tab');

            if (!textarea || !previewContent) return;

            // 配置marked选项
            marked.setOptions({
                breaks: true,
                gfm: true,
                headerIds: false,
                mangle: false
            });

            // 更新预览内容
            function updatePreview() {
                const markdownText = textarea.value;
                if (markdownText.trim() === '') {
                    previewContent.innerHTML = '<p class="text-muted">开始输入以查看预览...</p>';
                } else {
                    try {
                        const html = marked.parse(markdownText);
                        const cleanHtml = DOMPurify.sanitize(html);
                        previewContent.innerHTML = cleanHtml;
                    } catch (error) {
                        previewContent.innerHTML = '<p class="text-danger">预览解析错误</p>';
                    }
                }
            }

            // 切换视图模式
            function switchTab(mode) {
                // 更新tab状态
                tabs.forEach(tab => {
                    tab.classList.remove('active');
                    if (tab.dataset.tab === mode) {
                        tab.classList.add('active');
                    }
                });

                // 切换面板显示
                switch (mode) {
                    case 'edit':
                        editorPane.classList.remove('hidden');
                        previewPane.classList.add('hidden');
                        textarea.focus();
                        break;
                    case 'preview':
                        editorPane.classList.add('hidden');
                        previewPane.classList.remove('hidden');
                        updatePreview();
                        break;
                    case 'split':
                        editorPane.classList.remove('hidden');
                        previewPane.classList.remove('hidden');
                        updatePreview();
                        break;
                }
            }

            // 绑定tab点击事件
            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    switchTab(this.dataset.tab);
                });
            });

            // 绑定textarea输入事件
            textarea.addEventListener('input', function() {
                // 只在分屏模式下实时更新
                if (!previewPane.classList.contains('hidden')) {
                    updatePreview();
                }
            });

            // 初始化时如果有内容就更新预览
            if (textarea.value.trim() !== '') {
                updatePreview();
            }

            // 键盘快捷键
            textarea.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + P 切换预览
                if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
                    e.preventDefault();
                    const currentTab = document.querySelector('.editor-tab.active').dataset.tab;
                    if (currentTab === 'edit') {
                        switchTab('preview');
                    } else if (currentTab === 'preview') {
                        switchTab('edit');
                    }
                }

                // Ctrl/Cmd + Shift + P 切换分屏
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'P') {
                    e.preventDefault();
                    switchTab('split');
                }

                // Tab键插入缩进
                if (e.key === 'Tab') {
                    e.preventDefault();
                    const start = this.selectionStart;
                    const end = this.selectionEnd;
                    this.value = this.value.substring(0, start) + '    ' + this.value.substring(end);
                    this.selectionStart = this.selectionEnd = start + 4;
                }
            });
        });
    </script>
{% endblock %}