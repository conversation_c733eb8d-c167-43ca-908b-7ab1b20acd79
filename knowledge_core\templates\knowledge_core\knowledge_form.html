{% extends 'base.html' %}
{% load static %}

{% block title %}
    {% if form.instance.pk %}
        编辑知识条目 - {{ form.instance.title }}
    {% else %}
        创建新知识条目
    {% endif %}
{% endblock %}

{% block extra_css %}
    <!-- 在 <head> 或模板顶部加入 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/editor.md/css/editormd.min.css" />
{% endblock %}

{% block content %}
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-7">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            {% if form.instance.pk %}
                                编辑知识条目
                            {% else %}
                                创建新知识条目
                            {% endif %}
                        </h4>
                    </div>
                    <div class="card-body">
                        <form method="post" enctype="multipart/form-data">
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                                <div class="alert alert-danger" role="alert">
                                    {% for error in form.non_field_errors %}
                                        <p class="mb-0">{{ error }}</p>
                                    {% endfor %}
                                </div>
                            {% endif %}

                            {# 遍历表单字段并有条件地渲染它们 #}
                            {% for field in form %}
                                {# 只渲染不是 slug、created_by、created_at、updated_at 的字段 #}
                                {% if field.name != 'slug' and field.name != 'created_by' and field.name != 'created_at' and field.name != 'updated_at' %}
                                    <div class="mb-3">
                                        <label for="{{ field.id_for_label }}" class="form-label">
                                            {{ field.label }}
                                            {% if field.field.required %}
                                                <span class="text-danger">*</span>
                                            {% endif %}
                                        </label>

                                        {{ field }}

                                        {% if field.help_text %}
                                            <div class="form-text text-muted">{{ field.help_text }}</div>
                                        {% endif %}

                                        {% if field.errors %}
                                            <div class="invalid-feedback d-block">
                                                {% for error in field.errors %}
                                                    {{ error }}
                                                {% endfor %}
                                            </div>
                                        {% endif %}
                                    </div>
                                {% endif %} {# 字段条件渲染的结束 #}
                            {% endfor %}

                            <hr>
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    {% if form.instance.pk %}
                                        更新知识
                                    {% else %}
                                        创建知识
                                    {% endif %}
                                </button>
                                {% if form.instance.pk %}
                                    <a href="{{ form.instance.get_absolute_url }}" class="btn btn-outline-secondary">取消</a>
                                {% else %}
                                    <a href="{% url 'knowledge_core:index' %}" class="btn btn-outline-secondary">取消</a>
                                {% endif %}
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block extra_js %}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('.invalid-feedback').forEach(function(errorDiv) {
                const input = errorDiv.previousElementSibling;
                if (input && (input.tagName === 'INPUT' || input.tagName === 'TEXTAREA' || input.tagName === 'SELECT')) {
                    input.classList.add('is-invalid');
                }
            });

            // 确保如果小部件没有自动添加 Bootstrap 类，则手动添加
            document.querySelectorAll('select').forEach(function(selectElement) {
                selectElement.classList.add('form-select');
            });
            document.querySelectorAll('input:not([type="checkbox"]):not([type="radio"]):not([type="hidden"]), textarea').forEach(function(inputElement) {
                inputElement.classList.add('form-control');
            });
            document.querySelectorAll('input[type="checkbox"], input[type="radio"]').forEach(function(inputElement) {
                inputElement.classList.add('form-check-input');
            });
        });
    </script>
{% endblock %}