{% extends "base.html" %}

{% block title %}登录{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white text-center">
                    <h4 class="mb-0">
                        <i class="bi bi-person-circle"></i> 用户登录
                    </h4>
                </div>
                <div class="card-body">
                    {% if form.errors %}
                        <div class="alert alert-danger" role="alert">
                            {% for field, errors in form.errors.items %}
                                {% for error in errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            {% endfor %}
                        </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        
                        <!-- 用户名 -->
                        <div class="mb-3">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="bi bi-person"></i> 用户名
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="{{ form.username.id_for_label }}" 
                                   name="{{ form.username.name }}" 
                                   value="{{ form.username.value|default:'' }}"
                                   required>
                        </div>

                        <!-- 密码 -->
                        <div class="mb-3">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="bi bi-lock"></i> 密码
                            </label>
                            <input type="password" 
                                   class="form-control" 
                                   id="{{ form.password.id_for_label }}" 
                                   name="{{ form.password.name }}"
                                   required>
                        </div>

                        <!-- 记住我 -->
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                记住我
                            </label>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-box-arrow-in-right"></i> 登录
                            </button>
                        </div>

                        <!-- 隐藏字段：重定向URL -->
                        {% if next %}
                            <input type="hidden" name="next" value="{{ next }}">
                        {% endif %}
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        还没有账户？<a href="{% url 'knowledge_core:signup' %}">立即注册</a>
                    </small>
                </div>
            </div>

            <!-- 帮助信息 -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="bi bi-info-circle"></i> 登录说明
                    </h6>
                    <ul class="mb-0 small text-muted">
                        <li>请使用管理员提供的用户名和密码登录</li>
                        <li>登录后可以创建和编辑知识条目</li>
                        <li>如果忘记密码，请联系管理员重置</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
