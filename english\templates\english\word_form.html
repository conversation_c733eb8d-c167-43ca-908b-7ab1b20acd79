{% extends "base.html" %}

{% block title %}
    {% if form.instance.pk %}
        编辑单词 - {{ form.instance.name }}
    {% else %}
        添加新单词
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-book"></i>
                        {% if form.instance.pk %}
                            编辑单词
                        {% else %}
                            添加新单词
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- 基本信息 -->
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="text-danger">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.name.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.name.help_text %}
                                    <div class="form-text">{{ form.name.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 音标 -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phonetic_us.id_for_label }}" class="form-label">
                                    {{ form.phonetic_us.label }}
                                </label>
                                {{ form.phonetic_us }}
                                {% if form.phonetic_us.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.phonetic_us.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.phonetic_us.help_text %}
                                    <div class="form-text">{{ form.phonetic_us.help_text }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.phonetic_uk.id_for_label }}" class="form-label">
                                    {{ form.phonetic_uk.label }}
                                </label>
                                {{ form.phonetic_uk }}
                                {% if form.phonetic_uk.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.phonetic_uk.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.phonetic_uk.help_text %}
                                    <div class="form-text">{{ form.phonetic_uk.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 音频 -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.audio_us.id_for_label }}" class="form-label">
                                    {{ form.audio_us.label }}
                                </label>
                                {{ form.audio_us }}
                                {% if form.audio_us.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.audio_us.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.audio_us.help_text %}
                                    <div class="form-text">{{ form.audio_us.help_text }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="{{ form.audio_uk.id_for_label }}" class="form-label">
                                    {{ form.audio_uk.label }}
                                </label>
                                {{ form.audio_uk }}
                                {% if form.audio_uk.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.audio_uk.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.audio_uk.help_text %}
                                    <div class="form-text">{{ form.audio_uk.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 词形变化 -->
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.past_tense.id_for_label }}" class="form-label">
                                    {{ form.past_tense.label }}
                                </label>
                                {{ form.past_tense }}
                                {% if form.past_tense.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.past_tense.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.past_tense.help_text %}
                                    <div class="form-text">{{ form.past_tense.help_text }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.past_participle.id_for_label }}" class="form-label">
                                    {{ form.past_participle.label }}
                                </label>
                                {{ form.past_participle }}
                                {% if form.past_participle.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.past_participle.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.past_participle.help_text %}
                                    <div class="form-text">{{ form.past_participle.help_text }}</div>
                                {% endif %}
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="{{ form.plural_form.id_for_label }}" class="form-label">
                                    {{ form.plural_form.label }}
                                </label>
                                {{ form.plural_form }}
                                {% if form.plural_form.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.plural_form.errors %}
                                            <div>{{ error }}</div>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                {% if form.plural_form.help_text %}
                                    <div class="form-text">{{ form.plural_form.help_text }}</div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- 例句 -->
                        <div class="mb-3">
                            <label for="{{ form.example_sentences.id_for_label }}" class="form-label">
                                {{ form.example_sentences.label }}
                            </label>
                            {{ form.example_sentences }}
                            {% if form.example_sentences.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.example_sentences.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.example_sentences.help_text %}
                                <div class="form-text">{{ form.example_sentences.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- 标签 -->
                        <div class="mb-4">
                            <label for="{{ form.tags.id_for_label }}" class="form-label">
                                {{ form.tags.label }}
                            </label>
                            {{ form.tags }}
                            {% if form.tags.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.tags.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            {% if form.tags.help_text %}
                                <div class="form-text">{{ form.tags.help_text }}</div>
                            {% endif %}
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-between">
                            <a href="{% if form.instance.pk %}{% url 'english:word_detail' form.instance.slug %}{% else %}{% url 'english:word_list' %}{% endif %}" 
                               class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {% if form.instance.pk %}
                                    更新单词
                                {% else %}
                                    添加单词
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 帮助信息 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-info-circle"></i> 填写提示</h6>
                    <ul class="mb-0 small text-muted">
                        <li>单词名称是必填项，其他字段可选</li>
                        <li>音标建议使用国际音标格式</li>
                        <li>音频文件请提供有效的URL链接</li>
                        <li>词形变化仅适用于相应的词性</li>
                        <li>例句有助于理解单词的实际用法</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
