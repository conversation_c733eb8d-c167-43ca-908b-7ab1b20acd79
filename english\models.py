from django.db import models


class Tag(models.Model):
    name = models.Char<PERSON>ield(max_length=50, unique=True)

    def __str__(self):
        return self.name


class Word(models.Model):
    name = models.CharField(max_length=100, unique=True)
    phonetic_us = models.CharField(max_length=100, blank=True)
    phonetic_uk = models.CharField(max_length=100, blank=True)
    audio_us = models.URLField(blank=True)
    audio_uk = models.URLField(blank=True)
    past_tense = models.CharField(max_length=100, blank=True)
    past_participle = models.CharField(max_length=100, blank=True)
    plural_form = models.CharField(max_length=100, blank=True)
    example_sentences = models.TextField(blank=True)  # 可考虑 Markdown 存储
    tags = models.ManyToManyField(Tag, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class Meaning(models.Model):
    word = models.ForeignKey(Word, on_delete=models.CASCADE, related_name="meanings")
    PART_OF_SPEECH_CHOICES = [
        ('n', '名词 (Noun)'),
        ('v', '动词 (Verb)'),
        ('adj', '形容词 (Adjective)'),
        ('adv', '副词 (Adverb)'),
        ('prep', '介词 (Preposition)'),
        ('conj', '连词 (Conjunction)'),
        ('pron', '代词 (Pronoun)'),
        ('intj', '感叹词 (Interjection)'),
        ('art', '冠词 (Article)'),
        ('phr', '短语 (Phrase)'), # 针对词组
        ('idiom', '习语 (Idiom)'), # 针对习语
        ('other', '其他 (Other)'),
    ]
    part_of_speech = models.CharField(
        max_length=10,
        choices=PART_OF_SPEECH_CHOICES,
        blank=True,
        verbose_name="词性"
    )
    translation = models.CharField(max_length=255, verbose_name="释义")

    def __str__(self):
        return f"{self.word.name}: {self.part_of_speech} {self.translation}"



