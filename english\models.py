from django.db import models
from django.contrib.auth.models import User
from django.urls import reverse
from slugify import slugify


class Tag(models.Model):
    name = models.CharField(max_length=50, unique=True)
    slug = models.SlugField(max_length=60, unique=True, blank=True)

    class Meta:
        ordering = ['name']

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.name)
            slug = base_slug
            num = 1
            while Tag.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('english:tag_detail', args=[self.slug])


class Word(models.Model):
    name = models.CharField(max_length=100, unique=True, verbose_name="单词")
    slug = models.SlugField(max_length=120, unique=True, blank=True)
    phonetic_us = models.CharField(max_length=100, blank=True, verbose_name="美式音标")
    phonetic_uk = models.CharField(max_length=100, blank=True, verbose_name="英式音标")
    audio_us = models.URLField(blank=True, verbose_name="美式发音")
    audio_uk = models.URLField(blank=True, verbose_name="英式发音")
    past_tense = models.CharField(max_length=100, blank=True, verbose_name="过去式")
    past_participle = models.CharField(max_length=100, blank=True, verbose_name="过去分词")
    plural_form = models.CharField(max_length=100, blank=True, verbose_name="复数形式")
    example_sentences = models.TextField(blank=True, verbose_name="例句")
    tags = models.ManyToManyField(Tag, blank=True, verbose_name="标签")
    author = models.ForeignKey(User, on_delete=models.CASCADE, verbose_name="创建者")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        ordering = ['name']
        verbose_name = "单词"
        verbose_name_plural = "单词"

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        if not self.slug:
            base_slug = slugify(self.name)
            slug = base_slug
            num = 1
            while Word.objects.filter(slug=slug).exists():
                slug = f"{base_slug}-{num}"
                num += 1
            self.slug = slug
        super().save(*args, **kwargs)

    def get_absolute_url(self):
        return reverse('english:word_detail', args=[self.slug])

    def get_edit_url(self):
        return reverse('english:word_edit', args=[self.slug])

    def get_delete_url(self):
        return reverse('english:word_delete', args=[self.slug])


class Meaning(models.Model):
    word = models.ForeignKey(Word, on_delete=models.CASCADE, related_name="meanings")
    PART_OF_SPEECH_CHOICES = [
        ('n', '名词 (Noun)'),
        ('v', '动词 (Verb)'),
        ('adj', '形容词 (Adjective)'),
        ('adv', '副词 (Adverb)'),
        ('prep', '介词 (Preposition)'),
        ('conj', '连词 (Conjunction)'),
        ('pron', '代词 (Pronoun)'),
        ('intj', '感叹词 (Interjection)'),
        ('art', '冠词 (Article)'),
        ('phr', '短语 (Phrase)'), # 针对词组
        ('idiom', '习语 (Idiom)'), # 针对习语
        ('other', '其他 (Other)'),
    ]
    part_of_speech = models.CharField(
        max_length=10,
        choices=PART_OF_SPEECH_CHOICES,
        blank=True,
        verbose_name="词性"
    )
    translation = models.CharField(max_length=255, verbose_name="释义")

    def __str__(self):
        return f"{self.word.name}: {self.part_of_speech} {self.translation}"



