{% extends "base.html" %}
{% load static %}

{% block title %}英语单词库{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/english.css' %}">
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>英语单词库</h1>
    <div>
        <a href="{% url 'english:word_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-circle"></i> 添加单词
        </a>
    </div>
</div>

<div class="row">
    <!-- 筛选侧边栏 -->
    <div class="col-md-3">
        <div class="card mb-4">
            <div class="card-header">
                <h5>筛选条件</h5>
            </div>
            <div class="card-body">
                <form method="get">
                    <div class="mb-3">
                        <label class="form-label">难度</label>
                        <select name="difficulty" class="form-select">
                            <option value="">全部</option>
                            <option value="1" {% if request.GET.difficulty == '1' %}selected{% endif %}>简单</option>
                            <option value="2" {% if request.GET.difficulty == '2' %}selected{% endif %}>中等</option>
                            <option value="3" {% if request.GET.difficulty == '3' %}selected{% endif %}>困难</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">掌握状态</label>
                        <select name="mastered" class="form-select">
                            <option value="">全部</option>
                            <option value="true" {% if request.GET.mastered == 'true' %}selected{% endif %}>已掌握</option>
                            <option value="false" {% if request.GET.mastered == 'false' %}selected{% endif %}>未掌握</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">词性分类</label>
                        <select name="category" class="form-select">
                            <option value="">全部</option>
                            {% for category in categories %}
                            <option value="{{ category.slug }}" {% if request.GET.category == category.slug %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">标签</label>
                        <select name="tag" class="form-select">
                            <option value="">全部</option>
                            {% for tag in tags %}
                            <option value="{{ tag.slug }}" {% if request.GET.tag == tag.slug %}selected{% endif %}>
                                {{ tag.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <button type="submit" class="btn btn-primary w-100">应用筛选</button>
                </form>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h5>统计信息</h5>
            </div>
            <div class="card-body">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        总单词数
                        <span class="badge bg-primary rounded-pill">{{ words.paginator.count }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        已掌握单词
                        <span class="badge bg-success rounded-pill">{{ mastered_count }}</span>
                    </li>
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        待复习单词
                        <span class="badge bg-warning rounded-pill">{{ review_count }}</span>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    
    <!-- 单词列表 -->
    <div class="col-md-9">
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-8">
                        <!-- <input type="text" name="search" class="form-control" 
                               placeholder="搜索单词、释义..." value="{{ request.GET.search }}"> -->
                    </div>
                    <div class="col-md-4">
                        <button type="submit" class="btn btn-outline-primary w-100">搜索</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="row row-cols-1 row-cols-md-2 g-4">
            {% for word in words %}
            <div class="col">
                <div class="card h-100 word-card {% if word.mastered %}border-success{% endif %}">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <h5 class="card-title">{{ word.name }}</h5>
                            {% if word.mastered %}
                            <span class="badge bg-success">已掌握</span>
                            {% endif %}
                        </div>
                        <h6 class="card-subtitle mb-2 text-muted">
                            {{ word.phonetic }} | {{ word.category.name }}
                        </h6>
                        <p class="card-text">{{ word.chinese_meaning|truncatechars:100 }}</p>
                        
                        <div class="d-flex flex-wrap mb-2">
                            {% for tag in word.tags.all %}
                            <span class="badge bg-secondary me-1 mb-1">{{ tag.name }}</span>
                            {% endfor %}
                        </div>
                        
                        <a href="{% url 'english:word_detail' word.id %}" class="btn btn-sm btn-outline-primary">
                            查看详情
                        </a>
                    </div>
                    <div class="card-footer text-muted">
                        <small>
                            难度: 
                            {% if word.difficulty == 1 %}
                            <span class="text-success">简单</span>
                            {% elif word.difficulty == 2 %}
                            <span class="text-warning">中等</span>
                            {% else %}
                            <span class="text-danger">困难</span>
                            {% endif %}
                            | 添加于: {{ word.created_at|date:"Y-m-d" }}
                        </small>
                    </div>
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="alert alert-info">没有找到匹配的单词</div>
            </div>
            {% endfor %}
        </div>
        
        <!-- 分页 -->
        {% if words.has_other_pages %}
        <nav class="mt-4">
            <ul class="pagination justify-content-center">
                {% if words.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ words.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">上一页</a>
                </li>
                {% endif %}
                
                {% for i in words.paginator.page_range %}
                {% if words.number == i %}
                <li class="page-item active"><span class="page-link">{{ i }}</span></li>
                {% else %}
                <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
                {% endif %}
                {% endfor %}
                
                {% if words.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ words.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">下一页</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>
{% endblock %}