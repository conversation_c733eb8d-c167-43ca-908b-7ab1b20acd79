{% extends "base.html" %}

{% block title %}{{ tag.name }} - 标签知识条目{% endblock %}

{% block content %}
<div class="container mt-4">
    <!-- 标签头部信息 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1><i class="bi bi-tag"></i> {{ tag.name }}</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'knowledge_core:knowledge_entry_list' %}">知识库</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">标签: {{ tag.name }}</li>
                </ol>
            </nav>
        </div>
        <div class="btn-group">
            <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 新建知识条目
            </a>
        </div>
    </div>

    <!-- 统计信息 -->
    <div class="alert alert-info" role="alert">
        <i class="bi bi-info-circle"></i>
        共找到 <strong>{{ knowledges|length }}</strong> 个包含标签 "{{ tag.name }}" 的知识条目
    </div>

    <!-- 知识条目列表 -->
    {% if knowledges %}
        <div class="row">
            {% for knowledge in knowledges %}
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" class="text-decoration-none">
                                {{ knowledge.title }}
                            </a>
                        </h5>
                        
                        {% if knowledge.content %}
                        <p class="card-text text-muted">
                            {{ knowledge.content|truncatewords:20|striptags }}
                        </p>
                        {% endif %}
                        
                        <div class="mb-3">
                            <!-- 分类信息 -->
                            {% if knowledge.category %}
                            <span class="badge bg-primary me-2">
                                <i class="bi bi-folder"></i>
                                <a href="{% url 'knowledge_core:category_knowledges' knowledge.category.slug %}" class="text-white text-decoration-none">
                                    {{ knowledge.category.name }}
                                </a>
                            </span>
                            {% endif %}
                            
                            <!-- 其他标签 -->
                            {% for other_tag in knowledge.tags.all %}
                                {% if other_tag != tag %}
                                <a href="{% url 'knowledge_core:tag_knowledges' other_tag.slug %}" class="badge bg-secondary text-decoration-none me-1">
                                    {{ other_tag.name }}
                                </a>
                                {% endif %}
                            {% endfor %}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <small class="text-muted">
                                {{ knowledge.updated_at|date:"Y-m-d H:i" }}
                            </small>
                            <div class="btn-group btn-group-sm">
                                <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" 
                                   class="btn btn-outline-primary" title="查看">
                                    <i class="bi bi-eye"></i>
                                </a>
                                <a href="{% url 'knowledge_core:knowledge_edit' knowledge.slug %}" 
                                   class="btn btn-outline-secondary" title="编辑">
                                    <i class="bi bi-pencil"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-tag-x" style="font-size: 4rem; color: #6c757d;"></i>
            <h3 class="mt-3 text-muted">该标签下暂无知识条目</h3>
            <p class="text-muted">开始创建包含此标签的知识条目吧！</p>
            <a href="{% url 'knowledge_core:knowledge_create' %}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> 创建知识条目
            </a>
        </div>
    {% endif %}

    <!-- 返回按钮 -->
    <div class="mt-4">
        <a href="{% url 'knowledge_core:knowledge_entry_list' %}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left"></i> 返回知识库
        </a>
    </div>
</div>
{% endblock %}
