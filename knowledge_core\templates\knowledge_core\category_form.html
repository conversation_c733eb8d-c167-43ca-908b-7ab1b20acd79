{% extends "base.html" %}

{% block title %}
    {% if form.instance.pk %}
        编辑分类 - {{ form.instance.name }}
    {% else %}
        创建新分类
    {% endif %}
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-folder"></i>
                        {% if form.instance.pk %}
                            编辑分类
                        {% else %}
                            创建新分类
                        {% endif %}
                    </h4>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger" role="alert">
                                {% for error in form.non_field_errors %}
                                    <p class="mb-0">{{ error }}</p>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- 分类名称 -->
                        <div class="mb-3">
                            <label for="{{ form.name.id_for_label }}" class="form-label">
                                {{ form.name.label }} <span class="text-danger">*</span>
                            </label>
                            {{ form.name }}
                            {% if form.name.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.name.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">分类的名称，必须唯一</div>
                        </div>

                        <!-- 父分类 -->
                        <div class="mb-3">
                            <label for="{{ form.parent.id_for_label }}" class="form-label">
                                {{ form.parent.label }}
                            </label>
                            {{ form.parent }}
                            {% if form.parent.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.parent.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">选择父分类（可选），用于创建分类层次结构</div>
                        </div>

                        <!-- 描述 -->
                        <div class="mb-4">
                            <label for="{{ form.description.id_for_label }}" class="form-label">
                                {{ form.description.label }}
                            </label>
                            {{ form.description }}
                            {% if form.description.errors %}
                                <div class="text-danger small mt-1">
                                    {% for error in form.description.errors %}
                                        <div>{{ error }}</div>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <div class="form-text">分类的详细描述（可选）</div>
                        </div>

                        <!-- 提交按钮 -->
                        <div class="d-flex justify-content-between">
                            <a href="{% if form.instance.pk %}{% url 'knowledge_core:category_detail' form.instance.slug %}{% else %}{% url 'knowledge_core:category_list' %}{% endif %}" 
                               class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i>
                                {% if form.instance.pk %}
                                    更新分类
                                {% else %}
                                    创建分类
                                {% endif %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 帮助信息 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-info-circle"></i> 使用提示</h6>
                    <ul class="mb-0 small text-muted">
                        <li>分类名称必须唯一，不能与现有分类重复</li>
                        <li>可以通过设置父分类来创建分类层次结构</li>
                        <li>分类描述支持换行，可以详细说明分类的用途</li>
                        <li>创建后会自动生成URL友好的slug</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
