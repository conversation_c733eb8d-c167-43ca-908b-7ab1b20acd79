{% extends "base.html" %}

{% block title %}删除知识条目 - {{ knowledge.title }}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-sm">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> 确认删除知识条目
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning" role="alert">
                        <i class="bi bi-exclamation-triangle-fill"></i>
                        <strong>警告：</strong> 此操作不可撤销！
                    </div>

                    <p>您确定要删除知识条目 <strong>"{{ knowledge.title }}"</strong> 吗？</p>

                    <!-- 知识条目信息 -->
                    <div class="mb-3">
                        <h6>知识条目详情：</h6>
                        <ul class="list-unstyled">
                            <li><strong>标题：</strong> {{ knowledge.title }}</li>
                            {% if knowledge.category %}
                            <li><strong>分类：</strong> {{ knowledge.category.name }}</li>
                            {% endif %}
                            {% if knowledge.tags.exists %}
                            <li><strong>标签：</strong> 
                                {% for tag in knowledge.tags.all %}
                                    <span class="badge bg-secondary me-1">{{ tag.name }}</span>
                                {% endfor %}
                            </li>
                            {% endif %}
                            <li><strong>创建时间：</strong> {{ knowledge.created_at|date:"Y-m-d H:i" }}</li>
                            <li><strong>最后更新：</strong> {{ knowledge.updated_at|date:"Y-m-d H:i" }}</li>
                            <li><strong>创建者：</strong> {{ knowledge.created_by.username }}</li>
                        </ul>
                    </div>

                    <!-- 内容预览 -->
                    {% if knowledge.content %}
                    <div class="mb-3">
                        <h6>内容预览：</h6>
                        <div class="border rounded p-3 bg-light">
                            <p class="text-muted mb-0">{{ knowledge.content|truncatewords:30|striptags }}</p>
                        </div>
                    </div>
                    {% endif %}

                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'knowledge_core:knowledge_detail' knowledge.slug %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 取消
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> 确认删除
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 替代方案 -->
            <div class="card mt-4">
                <div class="card-body">
                    <h6 class="card-title"><i class="bi bi-lightbulb"></i> 替代方案</h6>
                    <p class="card-text small text-muted">
                        如果您只是想修改内容，可以考虑：
                    </p>
                    <ul class="small text-muted mb-0">
                        <li>编辑知识条目内容</li>
                        <li>更改分类或标签</li>
                        <li>将内容移动到其他知识条目</li>
                    </ul>
                    <div class="mt-2">
                        <a href="{% url 'knowledge_core:knowledge_edit' knowledge.slug %}" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-pencil"></i> 编辑知识条目
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
