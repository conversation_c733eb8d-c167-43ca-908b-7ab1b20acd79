from django import forms
from .models import Word, Tag, Meaning

class WordForm(forms.ModelForm):
    class Meta:
        model = Word
        exclude = ['slug', 'author', 'created_at', 'updated_at']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入英文单词...'
            }),
            'phonetic_us': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '例：/ˈhæpi/'
            }),
            'phonetic_uk': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '例：/ˈhæpi/'
            }),
            'audio_us': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://example.com/audio.mp3'
            }),
            'audio_uk': forms.URLInput(attrs={
                'class': 'form-control',
                'placeholder': 'https://example.com/audio.mp3'
            }),
            'past_tense': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '动词的过去式形式'
            }),
            'past_participle': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '动词的过去分词形式'
            }),
            'plural_form': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '名词的复数形式'
            }),
            'example_sentences': forms.Textarea(attrs={
                'class': 'form-control',
                'rows': 4,
                'placeholder': '请输入例句，每行一个例句...'
            }),
            'tags': forms.SelectMultiple(attrs={
                'class': 'form-select',
                'size': '5'
            }),
        }
        labels = {
            'name': '单词',
            'phonetic_us': '美式音标',
            'phonetic_uk': '英式音标',
            'audio_us': '美式发音',
            'audio_uk': '英式发音',
            'past_tense': '过去式',
            'past_participle': '过去分词',
            'plural_form': '复数形式',
            'example_sentences': '例句',
            'tags': '标签',
        }
        help_texts = {
            'name': '输入要学习的英文单词',
            'phonetic_us': '美式英语的音标标注',
            'phonetic_uk': '英式英语的音标标注',
            'audio_us': '美式发音音频文件的URL',
            'audio_uk': '英式发音音频文件的URL',
            'past_tense': '动词的过去式形式（如果适用）',
            'past_participle': '动词的过去分词形式（如果适用）',
            'plural_form': '名词的复数形式（如果适用）',
            'example_sentences': '包含该单词的例句，有助于理解用法',
            'tags': '为单词添加标签，便于分类和搜索',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['tags'].required = False


class MeaningForm(forms.ModelForm):
    class Meta:
        model = Meaning
        exclude = ['word']
        widgets = {
            'part_of_speech': forms.Select(attrs={'class': 'form-select'}),
            'translation': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入中文释义...'
            }),
        }
        labels = {
            'part_of_speech': '词性',
            'translation': '中文释义',
        }


class TagForm(forms.ModelForm):
    class Meta:
        model = Tag
        exclude = ['slug']
        widgets = {
            'name': forms.TextInput(attrs={
                'class': 'form-control',
                'placeholder': '请输入标签名称...'
            }),
        }
        labels = {
            'name': '标签名称',
        }
