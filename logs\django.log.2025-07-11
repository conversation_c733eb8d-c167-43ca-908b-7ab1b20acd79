[INFO] 2025-07-11 10:41:46,726 django.utils.autoreload | Watching for file changes with StatReloader
[WARNING] 2025-07-11 10:42:11,241 django.request | Not Found: /
[WARNING] 2025-07-11 10:42:11,243 django.server | "GET / HTTP/1.1" 404 15732
[INFO] 2025-07-11 10:42:11,314 django.server | "GET /static/debug_toolbar/css/toolbar.css HTTP/1.1" 304 0
[INFO] 2025-07-11 10:42:11,325 django.server | "GET /static/debug_toolbar/js/toolbar.js HTTP/1.1" 304 0
[INFO] 2025-07-11 10:42:11,779 django.server | "GET /static/debug_toolbar/css/print.css HTTP/1.1" 304 0
[INFO] 2025-07-11 10:42:11,821 django.server | "GET /static/debug_toolbar/js/utils.js HTTP/1.1" 304 0
[INFO] 2025-07-11 10:49:04,506 django.utils.autoreload | D:\projects\gongvip-note\gongvip_note\settings.py changed, reloading.
[INFO] 2025-07-11 10:49:07,800 django.utils.autoreload | Watching for file changes with StatReloader
[INFO] 2025-07-11 10:49:10,649 django.utils.autoreload | D:\projects\gongvip-note\gongvip_note\settings.py changed, reloading.
[INFO] 2025-07-11 10:49:13,304 django.utils.autoreload | Watching for file changes with StatReloader
[WARNING] 2025-07-11 10:49:58,461 django.request | Not Found: /
[WARNING] 2025-07-11 10:49:58,462 django.server | "GET / HTTP/1.1" 404 15732
[INFO] 2025-07-11 10:51:34,140 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 40677
[WARNING] 2025-07-11 10:51:34,252 django.server | "GET /static/images/knowledge-hero.svg HTTP/1.1" 404 1889
[INFO] 2025-07-11 11:08:32,309 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 40426
[WARNING] 2025-07-11 11:08:32,414 django.server | "GET /static/images/knowledge-hero.svg HTTP/1.1" 404 1889
[INFO] 2025-07-11 11:09:11,849 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 40421
[WARNING] 2025-07-11 11:09:11,944 django.server | "GET /static/images/knowledge-hero.svg HTTP/1.1" 404 1889
[INFO] 2025-07-11 17:06:13,498 django.utils.autoreload | Watching for file changes with StatReloader
[WARNING] 2025-07-11 17:06:22,750 django.request | Not Found: /
[WARNING] 2025-07-11 17:06:22,751 django.server | "GET / HTTP/1.1" 404 15732
[ERROR] 2025-07-11 17:06:34,987 django.request | Internal Server Error: /knowledge_core/index/
Traceback (most recent call last):
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
               ^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\projects\gongvip-note\knowledge_core\views.py", line 88, in index
    return render(request, 'knowledge_core/index.html', context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\shortcuts.py", line 25, in render
    content = loader.render_to_string(template_name, context, request, using=using)
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader.py", line 62, in render_to_string
    return template.render(context, request)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\backends\django.py", line 107, in render
    return self.template.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 171, in render
    return self._render(context)
           ^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\loader_tags.py", line 159, in render
    return compiled_parent._render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\test\utils.py", line 114, in instrumented_test_render
    return self.nodelist.render(context)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 1016, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\base.py", line 977, in render_annotated
    return self.render(context)
           ^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\template\defaulttags.py", line 480, in render
    url = reverse(view_name, args=args, kwargs=kwargs, current_app=current_app)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\base.py", line 98, in reverse
    resolved_url = resolver._reverse_with_prefix(view, prefix, *args, **kwargs)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "d:\projects\gongvip-note\.venv\Lib\site-packages\django\urls\resolvers.py", line 831, in _reverse_with_prefix
    raise NoReverseMatch(msg)
django.urls.exceptions.NoReverseMatch: Reverse for 'home' not found. 'home' is not a valid view function or pattern name.
[ERROR] 2025-07-11 17:06:35,270 django.server | "GET /knowledge_core/index/ HTTP/1.1" 500 161208
[INFO] 2025-07-11 17:07:24,988 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 32449
[WARNING] 2025-07-11 17:07:25,089 django.server | "GET /static/images/knowledge-hero.svg HTTP/1.1" 404 1889
[WARNING] 2025-07-11 17:08:32,483 django.request | Not Found: /.well-known/appspecific/com.chrome.devtools.json
[WARNING] 2025-07-11 17:08:32,484 django.server | "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 15942
[INFO] 2025-07-11 17:15:21,317 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 32825
[WARNING] 2025-07-11 17:15:21,490 django.server | "GET /static/images/knowledge-hero.svg HTTP/1.1" 404 1889
[WARNING] 2025-07-11 17:15:21,684 django.request | Not Found: /.well-known/appspecific/com.chrome.devtools.json
[WARNING] 2025-07-11 17:15:21,685 django.server | "GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 15942
[INFO] 2025-07-11 17:35:14,124 django.server | "GET /knowledge_core/index/ HTTP/1.1" 200 36728
[WARNING] 2025-07-11 17:35:14,206 django.server | "GET /static/images/knowledge-hero.svg HTTP/1.1" 404 1889
